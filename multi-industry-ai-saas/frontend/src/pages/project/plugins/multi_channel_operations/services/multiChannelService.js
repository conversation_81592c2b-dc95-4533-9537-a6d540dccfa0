import apiService from '../../../../../services/apiService';

/**
 * 全渠道运营助手服务
 */
class MultiChannelService {
  constructor() {
    this.baseUrl = '/plugins/multi-channel-operations';
  }

  // ==================== 运营总览 ====================
  
  /**
   * 获取运营总览数据
   */
  async getDashboardOverview(projectId) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/dashboard/overview`);
  }

  /**
   * 获取渠道性能数据
   */
  async getChannelPerformance(projectId, days = 7) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/dashboard/channel-performance`, {
      params: { days }
    });
  }

  /**
   * 获取操作趋势数据
   */
  async getOperationTrends(projectId, days = 30) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/dashboard/operation-trends`, {
      params: { days }
    });
  }

  /**
   * 获取AI智能洞察
   */
  async getAiInsights(projectId) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/dashboard/ai-insights`);
  }

  // ==================== 渠道管理 ====================
  
  /**
   * 获取渠道列表
   */
  async getChannels(projectId, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/channels`, {
      params
    });
  }

  /**
   * 获取渠道详情
   */
  async getChannelDetail(projectId, channelId) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/channels/${channelId}`);
  }

  /**
   * 创建渠道配置
   */
  async createChannelConfig(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/channels`, data);
  }

  /**
   * 更新渠道配置
   */
  async updateChannelConfig(projectId, channelId, data) {
    return await apiService.put(`/project/${projectId}${this.baseUrl}/channels/${channelId}`, data);
  }

  /**
   * 删除渠道配置
   */
  async deleteChannelConfig(projectId, channelId) {
    return await apiService.delete(`/project/${projectId}${this.baseUrl}/channels/${channelId}`);
  }

  /**
   * 测试渠道API连接
   */
  async testChannelConnection(projectId, channelId) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/channels/${channelId}/test-connection`);
  }

  /**
   * 同步渠道数据
   */
  async syncChannelData(projectId, channelId, syncType = 'all') {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/channels/${channelId}/sync`, {
      sync_type: syncType
    });
  }

  // ==================== 商品管理 ====================
  
  /**
   * 获取商品映射列表
   */
  async getProductMappings(projectId, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/products`, {
      params
    });
  }

  /**
   * 创建商品映射
   */
  async createProductMapping(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/products`, data);
  }

  /**
   * 更新商品映射
   */
  async updateProductMapping(projectId, mappingId, data) {
    return await apiService.put(`/project/${projectId}${this.baseUrl}/products/${mappingId}`, data);
  }

  /**
   * 删除商品映射
   */
  async deleteProductMapping(projectId, mappingId) {
    return await apiService.delete(`/project/${projectId}${this.baseUrl}/products/${mappingId}`);
  }

  /**
   * 批量同步商品
   */
  async batchSyncProducts(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/products/batch-sync`, data);
  }

  /**
   * 批量上架商品
   */
  async batchListProducts(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/products/batch-list`, data);
  }

  /**
   * 批量下架商品
   */
  async batchUnlistProducts(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/products/batch-unlist`, data);
  }

  // ==================== 定价策略 ====================
  
  /**
   * 获取定价策略列表
   */
  async getPricingStrategies(projectId, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/pricing/strategies`, {
      params
    });
  }

  /**
   * 创建定价策略
   */
  async createPricingStrategy(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/pricing/strategies`, data);
  }

  /**
   * 更新定价策略
   */
  async updatePricingStrategy(projectId, strategyId, data) {
    return await apiService.put(`/project/${projectId}${this.baseUrl}/pricing/strategies/${strategyId}`, data);
  }

  /**
   * 删除定价策略
   */
  async deletePricingStrategy(projectId, strategyId) {
    return await apiService.delete(`/project/${projectId}${this.baseUrl}/pricing/strategies/${strategyId}`);
  }

  /**
   * 获取AI定价建议
   */
  async getAiPricingSuggestions(projectId, productId, channelId) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/pricing/ai-suggestions`, {
      params: { product_id: productId, channel_id: channelId }
    });
  }

  /**
   * 批量调价
   */
  async batchUpdatePrices(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/pricing/batch-update`, data);
  }

  // ==================== 竞品监控 ====================
  
  /**
   * 获取竞品列表
   */
  async getCompetitors(projectId, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/competitors`, {
      params
    });
  }

  /**
   * 添加竞品监控
   */
  async addCompetitorMonitoring(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/competitors`, data);
  }

  /**
   * 更新竞品信息
   */
  async updateCompetitor(projectId, competitorId, data) {
    return await apiService.put(`/project/${projectId}${this.baseUrl}/competitors/${competitorId}`, data);
  }

  /**
   * 删除竞品监控
   */
  async deleteCompetitor(projectId, competitorId) {
    return await apiService.delete(`/project/${projectId}${this.baseUrl}/competitors/${competitorId}`);
  }

  /**
   * 获取竞品价格历史
   */
  async getCompetitorPriceHistory(projectId, competitorId, days = 30) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/competitors/${competitorId}/price-history`, {
      params: { days }
    });
  }

  /**
   * 手动更新竞品数据
   */
  async updateCompetitorData(projectId, competitorId) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/competitors/${competitorId}/update`);
  }

  // ==================== 自动化规则 ====================
  
  /**
   * 获取自动化规则列表
   */
  async getAutomationRules(projectId, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/automation/rules`, {
      params
    });
  }

  /**
   * 创建自动化规则
   */
  async createAutomationRule(projectId, data) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/automation/rules`, data);
  }

  /**
   * 更新自动化规则
   */
  async updateAutomationRule(projectId, ruleId, data) {
    return await apiService.put(`/project/${projectId}${this.baseUrl}/automation/rules/${ruleId}`, data);
  }

  /**
   * 删除自动化规则
   */
  async deleteAutomationRule(projectId, ruleId) {
    return await apiService.delete(`/project/${projectId}${this.baseUrl}/automation/rules/${ruleId}`);
  }

  /**
   * 启用/禁用自动化规则
   */
  async toggleAutomationRule(projectId, ruleId, isActive) {
    return await apiService.patch(`/project/${projectId}${this.baseUrl}/automation/rules/${ruleId}/toggle`, {
      is_active: isActive
    });
  }

  /**
   * 手动执行自动化规则
   */
  async executeAutomationRule(projectId, ruleId) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/automation/rules/${ruleId}/execute`);
  }

  // ==================== 数据分析 ====================
  
  /**
   * 获取销售分析数据
   */
  async getSalesAnalytics(projectId, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/analytics/sales`, {
      params
    });
  }

  /**
   * 获取渠道对比分析
   */
  async getChannelComparison(projectId, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/analytics/channel-comparison`, {
      params
    });
  }

  /**
   * 获取商品表现分析
   */
  async getProductPerformance(projectId, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/analytics/product-performance`, {
      params
    });
  }

  /**
   * 导出分析报告
   */
  async exportAnalyticsReport(projectId, reportType, params = {}) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/analytics/export/${reportType}`, {
      params,
      responseType: 'blob'
    });
  }

  // ==================== 插件设置 ====================
  
  /**
   * 获取插件设置
   */
  async getPluginSettings(projectId) {
    return await apiService.get(`/project/${projectId}${this.baseUrl}/settings`);
  }

  /**
   * 更新插件设置
   */
  async updatePluginSettings(projectId, data) {
    return await apiService.put(`/project/${projectId}${this.baseUrl}/settings`, data);
  }

  /**
   * 重置插件设置
   */
  async resetPluginSettings(projectId) {
    return await apiService.post(`/project/${projectId}${this.baseUrl}/settings/reset`);
  }
}

// 创建单例实例
const multiChannelService = new MultiChannelService();

export default multiChannelService;
