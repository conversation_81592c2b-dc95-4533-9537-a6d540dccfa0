import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Tooltip,
  Typography,
  Row,
  Col,
  Statistic,
  Alert
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  LineChartOutlined,
  EyeOutlined,
  RiseOutlined,
  FallOutlined,
} from '@ant-design/icons';
import { Line } from '@ant-design/plots';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { Option } = Select;

const CompetitorMonitoring = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [competitors, setCompetitors] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [priceHistoryVisible, setPriceHistoryVisible] = useState(false);
  const [editingCompetitor, setEditingCompetitor] = useState(null);
  const [selectedCompetitor, setSelectedCompetitor] = useState(null);
  const [priceHistory, setPriceHistory] = useState([]);
  const [form] = Form.useForm();

  useEffect(() => {
    loadCompetitors();
  }, [projectId]);

  const loadCompetitors = async () => {
    try {
      setLoading(true);
      const response = await multiChannelService.getCompetitors(projectId);
      setCompetitors(response.data || []);
    } catch (error) {
      console.error('加载竞品列表失败:', error);
      message.error('加载竞品列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingCompetitor(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingCompetitor(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await multiChannelService.deleteCompetitor(projectId, id);
      message.success('删除成功');
      loadCompetitors();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingCompetitor) {
        await multiChannelService.updateCompetitor(projectId, editingCompetitor.id, values);
        message.success('更新成功');
      } else {
        await multiChannelService.addCompetitorMonitoring(projectId, values);
        message.success('添加成功');
      }
      setModalVisible(false);
      loadCompetitors();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const handleViewPriceHistory = async (record) => {
    try {
      setSelectedCompetitor(record);
      const response = await multiChannelService.getCompetitorPriceHistory(projectId, record.id);
      setPriceHistory(response.data || []);
      setPriceHistoryVisible(true);
    } catch (error) {
      console.error('获取价格历史失败:', error);
      message.error('获取价格历史失败');
    }
  };

  const handleUpdateData = async (record) => {
    try {
      setLoading(true);
      await multiChannelService.updateCompetitorData(projectId, record.id);
      message.success('更新成功');
      loadCompetitors();
    } catch (error) {
      console.error('更新失败:', error);
      message.error('更新失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status) => {
    const statusMap = {
      'active': { color: 'success', text: '监控中' },
      'inactive': { color: 'default', text: '已停止' },
      'error': { color: 'error', text: '错误' }
    };
    
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getPriceTrend = (priceHistory) => {
    if (!priceHistory || priceHistory.length < 2) return null;
    
    const latest = priceHistory[priceHistory.length - 1];
    const previous = priceHistory[priceHistory.length - 2];
    
    if (latest.price > previous.price) {
      return <RiseOutlined style={{ color: '#ff4d4f' }} />;
    } else if (latest.price < previous.price) {
      return <FallOutlined style={{ color: '#52c41a' }} />;
    }
    return null;
  };

  const columns = [
    {
      title: '竞品信息',
      key: 'product_info',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.product_name}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.competitor_name}
          </Text>
        </div>
      )
    },
    {
      title: '平台',
      dataIndex: 'channel_platform',
      key: 'channel_platform',
      render: (platform) => (
        <Tag color="blue">{platform}</Tag>
      )
    },
    {
      title: '当前价格',
      key: 'current_price',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span style={{ fontSize: 16, fontWeight: 'bold' }}>
            ¥{record.current_price}
          </span>
          {getPriceTrend(record.price_history)}
        </div>
      )
    },
    {
      title: '监控状态',
      dataIndex: 'monitoring_status',
      key: 'monitoring_status',
      render: (status) => getStatusTag(status)
    },
    {
      title: '最后监控',
      dataIndex: 'last_monitored_at',
      key: 'last_monitored_at',
      render: (text) => text ? new Date(text).toLocaleString() : '从未监控'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看价格历史">
            <Button icon={<RiseOutlined />} onClick={() => handleViewPriceHistory(record)} />
          </Tooltip>
          
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Tooltip title="更新数据">
            <Button
              type="text"
              icon={<SyncOutlined />}
              onClick={() => handleUpdateData(record)}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个竞品监控吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          👁️ 竞品监控
        </Title>
        <Text type="secondary">
          监控竞争对手的商品价格和市场动态
        </Text>
      </div>

      {/* 监控概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="监控商品"
              value={competitors.length}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃监控"
              value={competitors.filter(c => c.monitoring_status === 'active').length}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="价格上涨"
              value={competitors.filter(c => {
                const trend = getPriceTrend(c.price_history);
                return trend && trend.props.style.color === '#ff4d4f';
              }).length}
              suffix="个"
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="价格下跌"
              value={competitors.filter(c => {
                const trend = getPriceTrend(c.price_history);
                return trend && trend.props.style.color === '#52c41a';
              }).length}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加竞品监控
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={loadCompetitors}
            >
              刷新
            </Button>
          </Space>
          
          <Space>
            <Text type="secondary">
              共 {competitors.length} 个竞品
            </Text>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={competitors}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 添加/编辑竞品模态框 */}
      <Modal
        title={editingCompetitor ? '编辑竞品监控' : '添加竞品监控'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="competitor_name"
                label="竞争对手"
                rules={[{ required: true, message: '请输入竞争对手名称' }]}
              >
                <Input placeholder="如：某某餐厅" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="product_name"
                label="商品名称"
                rules={[{ required: true, message: '请输入商品名称' }]}
              >
                <Input placeholder="竞品商品名称" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="channel_platform"
            label="监控平台"
            rules={[{ required: true, message: '请选择监控平台' }]}
          >
            <Select placeholder="选择要监控的平台">
              <Option value="meituan">美团</Option>
              <Option value="douyin">抖音</Option>
              <Option value="eleme">饿了么</Option>
              <Option value="jd">京东</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="external_url"
            label="商品链接"
            rules={[{ required: true, message: '请输入商品链接' }]}
          >
            <Input placeholder="竞品商品的链接地址" />
          </Form.Item>

          <Form.Item
            name="current_price"
            label="当前价格"
          >
            <Input
              type="number"
              placeholder="当前价格（可选，系统会自动获取）"
              addonBefore="¥"
            />
          </Form.Item>

          <Alert
            message="监控说明"
            description="系统将定期抓取竞品价格信息，如果价格发生变化会及时通知您。请确保提供的链接地址正确且可访问。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCompetitor ? '更新' : '添加'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 价格历史模态框 */}
      <Modal
        title={`${selectedCompetitor?.product_name} - 价格历史`}
        open={priceHistoryVisible}
        onCancel={() => setPriceHistoryVisible(false)}
        footer={null}
        width={800}
      >
        {priceHistory.length > 0 ? (
          <Line
            data={priceHistory.map(item => ({
              date: item.date,
              price: item.price
            }))}
            xField="date"
            yField="price"
            height={400}
            smooth={true}
            point={{ size: 4 }}
            tooltip={{
              formatter: (datum) => ({
                name: '价格',
                value: `¥${datum.price}`
              })
            }}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Text type="secondary">暂无价格历史数据</Text>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CompetitorMonitoring;
