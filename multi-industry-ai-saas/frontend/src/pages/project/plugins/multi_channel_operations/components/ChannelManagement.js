import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Badge,
  Typography,
  Row,
  Col,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { Option } = Select;

const ChannelManagement = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [channels, setChannels] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingChannel, setEditingChannel] = useState(null);
  const [form] = Form.useForm();

  // 平台配置选项
  const platformOptions = [
    { value: 'meituan_takeout', label: '美团外卖', icon: '🍔' },
    { value: 'meituan_group_buy', label: '美团团购', icon: '🎫' },
    { value: 'meituan_flash', label: '美团闪购', icon: '⚡' },
    { value: 'douyin_group_buy', label: '抖音团购', icon: '🎵' },
    { value: 'douyin_impulse', label: '抖音随心团', icon: '💫' },
    { value: 'eleme_retail', label: '饿了么零售', icon: '🛒' },
    { value: 'eleme_takeout', label: '饿了么外卖', icon: '🥡' },
    { value: 'jd_takeout', label: '京东外卖', icon: '📦' },
    { value: 'private_group_buy', label: '私域团购', icon: '👥' },
    { value: 'offline_retail', label: '线下零售', icon: '🏪' },
    { value: 'enterprise_client', label: '企业客户', icon: '🏢' }
  ];

  useEffect(() => {
    loadChannels();
  }, [projectId]);

  const loadChannels = async () => {
    try {
      setLoading(true);
      const response = await multiChannelService.getChannels(projectId);
      setChannels(response.data || []);
    } catch (error) {
      console.error('加载渠道列表失败:', error);
      message.error('加载渠道列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingChannel(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingChannel(record);
    form.setFieldsValue({
      ...record,
      platform_config: record.platform_config || {},
      sync_settings: record.sync_settings || {}
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await multiChannelService.deleteChannelConfig(projectId, id);
      message.success('删除成功');
      loadChannels();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingChannel) {
        await multiChannelService.updateChannelConfig(projectId, editingChannel.id, values);
        message.success('更新成功');
      } else {
        await multiChannelService.createChannelConfig(projectId, values);
        message.success('创建成功');
      }
      setModalVisible(false);
      loadChannels();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const handleTestConnection = async (record) => {
    try {
      setLoading(true);
      const response = await multiChannelService.testChannelConnection(projectId, record.id);
      if (response.success) {
        message.success('连接测试成功');
      } else {
        message.error(`连接测试失败: ${response.message}`);
      }
    } catch (error) {
      console.error('连接测试失败:', error);
      message.error('连接测试失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async (record) => {
    try {
      setLoading(true);
      await multiChannelService.syncChannelData(projectId, record.id);
      message.success('同步成功');
      loadChannels();
    } catch (error) {
      console.error('同步失败:', error);
      message.error('同步失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (record) => {
    if (!record.is_active) {
      return <Tag color="default">已禁用</Tag>;
    }
    if (!record.is_api_configured) {
      return <Tag color="warning" icon={<ExclamationCircleOutlined />}>未配置API</Tag>;
    }
    return <Tag color="success" icon={<CheckCircleOutlined />}>正常</Tag>;
  };

  const getPlatformInfo = (platformCode) => {
    const platform = platformOptions.find(p => p.value === platformCode);
    return platform || { label: platformCode, icon: '🔗' };
  };

  const columns = [
    {
      title: '渠道名称',
      dataIndex: 'custom_name',
      key: 'custom_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.service_name}
          </Text>
        </div>
      )
    },
    {
      title: '平台',
      dataIndex: 'platform_name',
      key: 'platform_name',
      render: (text, record) => {
        const platformInfo = getPlatformInfo(record.platform_code);
        return (
          <Space>
            <span style={{ fontSize: 16 }}>{platformInfo.icon}</span>
            <span>{platformInfo.label}</span>
          </Space>
        );
      }
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => getStatusTag(record)
    },
    {
      title: 'API配置',
      key: 'api_status',
      render: (_, record) => (
        <Badge
          status={record.is_api_configured ? 'success' : 'warning'}
          text={record.is_api_configured ? '已配置' : '未配置'}
        />
      )
    },
    {
      title: '最后同步',
      dataIndex: 'last_sync_at',
      key: 'last_sync_at',
      render: (text) => text ? new Date(text).toLocaleString() : '从未同步'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Tooltip title="API配置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          {record.is_api_configured && (
            <>
              <Tooltip title="测试连接">
                <Button
                  type="text"
                  icon={<ApiOutlined />}
                  onClick={() => handleTestConnection(record)}
                />
              </Tooltip>
              
              <Tooltip title="同步数据">
                <Button
                  type="text"
                  icon={<SyncOutlined />}
                  onClick={() => handleSync(record)}
                />
              </Tooltip>
            </>
          )}
          
          <Popconfirm
            title="确定要删除这个渠道配置吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          🏪 渠道管理
        </Title>
        <Text type="secondary">
          管理和配置所有销售渠道，设置API连接和同步规则
        </Text>
      </div>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加渠道
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={loadChannels}
            >
              刷新
            </Button>
          </Space>
          
          <Space>
            <Text type="secondary">
              共 {channels.length} 个渠道
            </Text>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={channels}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 渠道配置模态框 */}
      <Modal
        title={editingChannel ? '编辑渠道配置' : '添加渠道配置'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="custom_name"
                label="渠道名称"
                rules={[{ required: true, message: '请输入渠道名称' }]}
              >
                <Input placeholder="如：上海总店美团外卖" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="platform_type"
                label="平台类型"
                rules={[{ required: true, message: '请选择平台类型' }]}
              >
                <Select placeholder="选择平台类型">
                  {platformOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      <Space>
                        <span>{option.icon}</span>
                        <span>{option.label}</span>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={2} placeholder="渠道描述信息" />
          </Form.Item>

          <Divider>API配置</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['api_credentials', 'app_id']}
                label="App ID / Client Key"
              >
                <Input placeholder="应用ID或客户端密钥" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['api_credentials', 'app_secret']}
                label="App Secret / Client Secret"
              >
                <Input.Password placeholder="应用密钥或客户端秘钥" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['api_credentials', 'access_token']}
                label="Access Token"
              >
                <Input placeholder="访问令牌（如需要）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['api_credentials', 'store_id']}
                label="店铺ID"
              >
                <Input placeholder="平台店铺ID" />
              </Form.Item>
            </Col>
          </Row>

          <Divider>同步设置</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['sync_settings', 'auto_sync_products']}
                label="自动同步商品"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['sync_settings', 'auto_sync_orders']}
                label="自动同步订单"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['sync_settings', 'auto_sync_inventory']}
                label="自动同步库存"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name={['sync_settings', 'sync_frequency']}
            label="同步频率"
          >
            <Select placeholder="选择同步频率">
              <Option value="realtime">实时同步</Option>
              <Option value="hourly">每小时</Option>
              <Option value="daily">每天</Option>
              <Option value="manual">手动同步</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="is_active"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingChannel ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default ChannelManagement;
