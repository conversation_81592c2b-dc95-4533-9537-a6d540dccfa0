import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Typography,
  Row,
  Col,
  Divider,
  Badge
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  <PERSON>boltOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { Option } = Select;

const AutomationRules = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [rules, setRules] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRule, setEditingRule] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadRules();
  }, [projectId]);

  const loadRules = async () => {
    try {
      setLoading(true);
      const response = await multiChannelService.getAutomationRules(projectId);
      setRules(response.data || []);
    } catch (error) {
      console.error('加载自动化规则失败:', error);
      message.error('加载自动化规则失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingRule(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingRule(record);
    form.setFieldsValue({
      ...record,
      conditions: record.conditions || {},
      actions: record.actions || {}
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await multiChannelService.deleteAutomationRule(projectId, id);
      message.success('删除成功');
      loadRules();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingRule) {
        await multiChannelService.updateAutomationRule(projectId, editingRule.id, values);
        message.success('更新成功');
      } else {
        await multiChannelService.createAutomationRule(projectId, values);
        message.success('创建成功');
      }
      setModalVisible(false);
      loadRules();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const handleToggleRule = async (record) => {
    try {
      await multiChannelService.toggleAutomationRule(projectId, record.id, !record.is_active);
      message.success(record.is_active ? '规则已禁用' : '规则已启用');
      loadRules();
    } catch (error) {
      console.error('切换状态失败:', error);
      message.error('切换状态失败');
    }
  };

  const handleExecuteRule = async (record) => {
    try {
      setLoading(true);
      await multiChannelService.executeAutomationRule(projectId, record.id);
      message.success('规则执行成功');
      loadRules();
    } catch (error) {
      console.error('执行规则失败:', error);
      message.error('执行规则失败');
    } finally {
      setLoading(false);
    }
  };

  const getRuleTypeTag = (type) => {
    const typeMap = {
      'price_adjustment': { color: 'blue', text: '价格调整' },
      'inventory_sync': { color: 'green', text: '库存同步' },
      'product_listing': { color: 'orange', text: '商品上下架' },
      'competitor_response': { color: 'purple', text: '竞品响应' },
      'promotion_management': { color: 'red', text: '促销管理' }
    };
    
    const config = typeMap[type] || { color: 'default', text: type };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getStatusBadge = (isActive) => {
    return (
      <Badge
        status={isActive ? 'success' : 'default'}
        text={isActive ? '启用' : '禁用'}
      />
    );
  };

  const columns = [
    {
      title: '规则名称',
      dataIndex: 'rule_name',
      key: 'rule_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: '规则类型',
      dataIndex: 'rule_type',
      key: 'rule_type',
      render: (type) => getRuleTypeTag(type)
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => getStatusBadge(isActive)
    },
    {
      title: '执行次数',
      dataIndex: 'execution_count',
      key: 'execution_count',
      render: (count) => count || 0
    },
    {
      title: '最后执行',
      dataIndex: 'last_executed_at',
      key: 'last_executed_at',
      render: (text) => text ? new Date(text).toLocaleString() : '从未执行'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title={record.is_active ? '禁用' : '启用'}>
            <Button
              type="text"
              icon={record.is_active ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleToggleRule(record)}
            />
          </Tooltip>
          
          <Tooltip title="手动执行">
            <Button
              type="text"
              icon={<ThunderboltOutlined />}
              onClick={() => handleExecuteRule(record)}
              disabled={!record.is_active}
            />
          </Tooltip>
          
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个自动化规则吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          🤖 自动化规则
        </Title>
        <Text type="secondary">
          创建和管理自动化运营规则，实现智能化操作
        </Text>
      </div>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              创建规则
            </Button>
            <Button
              icon={<RobotOutlined />}
            >
              规则模板
            </Button>
          </Space>
          
          <Space>
            <Text type="secondary">
              共 {rules.length} 个规则，
              {rules.filter(r => r.is_active).length} 个启用
            </Text>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={rules}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 自动化规则模态框 */}
      <Modal
        title={editingRule ? '编辑自动化规则' : '创建自动化规则'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="rule_name"
                label="规则名称"
                rules={[{ required: true, message: '请输入规则名称' }]}
              >
                <Input placeholder="如：库存不足自动下架" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="rule_type"
                label="规则类型"
                rules={[{ required: true, message: '请选择规则类型' }]}
              >
                <Select placeholder="选择规则类型">
                  <Option value="price_adjustment">价格调整</Option>
                  <Option value="inventory_sync">库存同步</Option>
                  <Option value="product_listing">商品上下架</Option>
                  <Option value="competitor_response">竞品响应</Option>
                  <Option value="promotion_management">促销管理</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="规则描述"
          >
            <Input.TextArea rows={2} placeholder="描述这个自动化规则的用途" />
          </Form.Item>

          <Divider>触发条件</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['conditions', 'trigger_type']}
                label="触发类型"
                rules={[{ required: true, message: '请选择触发类型' }]}
              >
                <Select placeholder="选择触发类型">
                  <Option value="time_based">定时触发</Option>
                  <Option value="event_based">事件触发</Option>
                  <Option value="condition_based">条件触发</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['conditions', 'frequency']}
                label="执行频率"
              >
                <Select placeholder="选择执行频率">
                  <Option value="realtime">实时</Option>
                  <Option value="hourly">每小时</Option>
                  <Option value="daily">每天</Option>
                  <Option value="weekly">每周</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['conditions', 'inventory_threshold']}
                label="库存阈值"
              >
                <Input
                  type="number"
                  placeholder="库存低于此值时触发"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['conditions', 'price_change_threshold']}
                label="价格变动阈值(%)"
              >
                <Input
                  type="number"
                  placeholder="价格变动超过此百分比时触发"
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>执行动作</Divider>

          <Form.Item
            name={['actions', 'action_type']}
            label="执行动作"
            rules={[{ required: true, message: '请选择执行动作' }]}
          >
            <Select placeholder="选择要执行的动作">
              <Option value="adjust_price">调整价格</Option>
              <Option value="update_inventory">更新库存</Option>
              <Option value="list_product">上架商品</Option>
              <Option value="unlist_product">下架商品</Option>
              <Option value="send_notification">发送通知</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['actions', 'adjustment_value']}
                label="调整数值"
              >
                <Input placeholder="调整的具体数值" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['actions', 'target_channels']}
                label="目标渠道"
              >
                <Select mode="multiple" placeholder="选择要应用的渠道">
                  {/* 这里应该加载渠道列表 */}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name={['actions', 'notification_message']}
            label="通知消息"
          >
            <Input.TextArea rows={2} placeholder="执行动作时发送的通知消息" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="启用规则"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingRule ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AutomationRules;
