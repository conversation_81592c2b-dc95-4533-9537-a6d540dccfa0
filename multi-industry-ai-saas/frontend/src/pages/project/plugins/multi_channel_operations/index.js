import React, { useState, useEffect } from 'react';
import { Layout, Menu, Spin, message } from 'antd';
import { 
  DashboardOutlined, 
  ShopOutlined, 
  ShoppingOutlined,
  DollarOutlined,
  EyeOutlined,
  RobotOutlined,
  BarChartOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useParams, useNavigate, useLocation } from 'react-router-dom';

// 导入子页面组件
import Dashboard from './components/Dashboard';
import ChannelManagement from './components/ChannelManagement';
import ProductManagement from './components/ProductManagement';
import PricingStrategy from './components/PricingStrategy';
import CompetitorMonitoring from './components/CompetitorMonitoring';
import AutomationRules from './components/AutomationRules';
import Analytics from './components/Analytics';
import Settings from './components/Settings';

const { Sider, Content } = Layout;

const MultiChannelOperations = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState('dashboard');

  // 菜单项配置
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '运营总览',
      component: Dashboard
    },
    {
      key: 'channels',
      icon: <ShopOutlined />,
      label: '渠道管理',
      component: ChannelManagement
    },
    {
      key: 'products',
      icon: <ShoppingOutlined />,
      label: '商品管理',
      component: ProductManagement
    },
    {
      key: 'pricing',
      icon: <DollarOutlined />,
      label: '定价策略',
      component: PricingStrategy
    },
    {
      key: 'competitors',
      icon: <EyeOutlined />,
      label: '竞品监控',
      component: CompetitorMonitoring
    },
    {
      key: 'automation',
      icon: <RobotOutlined />,
      label: '自动化规则',
      component: AutomationRules
    },
    {
      key: 'analytics',
      icon: <BarChartOutlined />,
      label: '数据分析',
      component: Analytics
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '插件设置',
      component: Settings
    }
  ];

  // 根据路径设置选中的菜单项
  useEffect(() => {
    const pathSegments = location.pathname.split('/');
    const lastSegment = pathSegments[pathSegments.length - 1];

    // 如果路径中包含具体的页面名称，设置对应的菜单项
    const foundItem = menuItems.find(item =>
      location.pathname.includes(`/multi-channel-operations/${item.key}`) ||
      lastSegment === item.key
    );

    if (foundItem) {
      setSelectedKey(foundItem.key);
    } else {
      // 默认选中总览
      setSelectedKey('dashboard');
    }
  }, [location.pathname]);

  // 处理菜单点击
  const handleMenuClick = ({ key }) => {
    setSelectedKey(key);
    navigate(`/project/${projectId}/plugin/multi-channel-operations/${key}`);
  };

  // 获取当前选中的组件
  const getCurrentComponent = () => {
    const currentItem = menuItems.find(item => item.key === selectedKey);
    if (currentItem && currentItem.component) {
      const Component = currentItem.component;
      return <Component />;
    }
    return <Dashboard />;
  };

  return (
    <div style={{ height: '100vh', background: '#f0f2f5' }}>
      <Layout style={{ height: '100%' }}>
        {/* 侧边栏 */}
        <Sider
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          width={250}
          style={{
            background: '#fff',
            boxShadow: '2px 0 8px rgba(0,0,0,0.1)'
          }}
        >
          {/* 插件标题 */}
          <div style={{
            height: 64,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderBottom: '1px solid #f0f0f0',
            background: '#1890ff',
            color: '#fff'
          }}>
            {!collapsed ? (
              <div style={{ fontSize: 16, fontWeight: 'bold' }}>
                🛒 全渠道运营助手
              </div>
            ) : (
              <div style={{ fontSize: 20 }}>🛒</div>
            )}
          </div>

          {/* 菜单 */}
          <Menu
            mode="inline"
            selectedKeys={[selectedKey]}
            onClick={handleMenuClick}
            style={{ 
              height: 'calc(100% - 64px)',
              borderRight: 0,
              paddingTop: 16
            }}
            items={menuItems.map(item => ({
              key: item.key,
              icon: item.icon,
              label: item.label
            }))}
          />
        </Sider>

        {/* 主内容区 */}
        <Layout>
          <Content style={{
            margin: 0,
            padding: 0,
            background: '#f0f2f5',
            overflow: 'auto'
          }}>
            <Spin spinning={loading} tip="加载中...">
              <div style={{ 
                minHeight: '100%',
                padding: '24px',
                background: '#f0f2f5'
              }}>
                {getCurrentComponent()}
              </div>
            </Spin>
          </Content>
        </Layout>
      </Layout>
    </div>
  );
};

export default MultiChannelOperations;
