#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手插件数据库初始化
"""

import uuid
import logging
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import text

from models.plugin import Plugin
from db.database import get_db

logger = logging.getLogger(__name__)

# 插件信息
PLUGIN_INFO = {
    "code": "multi_channel_operations",
    "name": "全渠道运营助手",
    "description": "统一管理多个销售渠道，提供智能运营决策、自动化操作、竞品监控等功能。支持美团、抖音、饿了么等主流平台，实现线上线下联动、一品多渠道、一品多策略的自动化AI运营。",
    "version": "1.0.0",
    "type": "marketplace",
    "category": "operations",
    "icon_url": "/static/plugins/multi_channel_operations/icon.png",
    "price": 299,  # 付费插件
    "billing_cycle": "monthly",
    "author": "Multi-Industry AI SaaS Team",
    "homepage": None,
    "features": [
        "🎯 全渠道统一管理 - 一站式管理所有销售渠道",
        "🤖 AI智能定价 - 基于市场分析的动态定价策略", 
        "👁️ 竞品监控 - 实时监控竞争对手价格和商品",
        "⚡ 自动化操作 - 商品上下架、价格调整自动化",
        "📊 数据分析 - 多维度销售数据分析和预测",
        "🔗 API集成 - 对接主流电商平台开放API",
        "📱 移动端支持 - 随时随地管理渠道运营",
        "🔔 智能提醒 - 异常情况和优化建议推送",
        "🏪 支持平台 - 美团、抖音、饿了么、京东等主流平台",
        "🎮 游戏化运营 - 运营任务游戏化，提升团队积极性"
    ]
}

async def init_plugin():
    """初始化插件到数据库"""
    try:
        async for db in get_db():
            # 检查插件是否已存在
            plugin_query = select(Plugin).where(Plugin.code == PLUGIN_INFO["code"])
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()

            if not plugin:
                # 注册插件
                plugin = Plugin(
                    id=uuid.uuid4(),
                    code=PLUGIN_INFO["code"],
                    name=PLUGIN_INFO["name"],
                    description=PLUGIN_INFO["description"],
                    version=PLUGIN_INFO["version"],
                    category=PLUGIN_INFO["category"],
                    icon_url=PLUGIN_INFO["icon_url"],
                    price=PLUGIN_INFO["price"],
                    billing_cycle=PLUGIN_INFO["billing_cycle"],
                    is_system=False,
                    is_active=True,
                    author=PLUGIN_INFO["author"],
                    website=PLUGIN_INFO["homepage"],
                    installation_path="plugins.multi_channel_operations",
                    entry_point="initialize",
                    created_at=datetime.now(),
                    settings_schema={
                        "type": "object",
                        "properties": {
                            "enable_auto_pricing": {
                                "type": "boolean",
                                "title": "启用自动定价",
                                "description": "基于AI分析自动调整商品价格",
                                "default": True
                            },
                            "enable_competitor_monitoring": {
                                "type": "boolean", 
                                "title": "启用竞品监控",
                                "description": "监控竞争对手的价格和商品信息",
                                "default": True
                            },
                            "enable_auto_listing": {
                                "type": "boolean",
                                "title": "启用自动上下架",
                                "description": "根据库存和销售情况自动上下架商品",
                                "default": False
                            },
                            "price_adjustment_threshold": {
                                "type": "number",
                                "title": "价格调整阈值(%)",
                                "description": "价格调整的最大幅度百分比",
                                "minimum": 1,
                                "maximum": 50,
                                "default": 10
                            },
                            "monitoring_frequency": {
                                "type": "string",
                                "title": "监控频率",
                                "description": "竞品监控的执行频率",
                                "enum": ["hourly", "daily", "weekly"],
                                "default": "daily"
                            },
                            "supported_platforms": {
                                "type": "array",
                                "title": "支持的平台",
                                "description": "选择要管理的渠道平台",
                                "items": {
                                    "type": "string",
                                    "enum": [
                                        "meituan_takeout", "meituan_group_buy", "meituan_flash",
                                        "douyin_group_buy", "douyin_impulse", 
                                        "eleme_retail", "eleme_takeout",
                                        "jd_takeout", "private_group_buy",
                                        "offline_retail", "enterprise_client"
                                    ]
                                },
                                "default": ["meituan_takeout", "douyin_group_buy", "eleme_retail"]
                            },
                            "ai_assistant_integration": {
                                "type": "boolean",
                                "title": "AI助手集成",
                                "description": "启用AI助手进行智能运营建议",
                                "default": True
                            },
                            "notification_settings": {
                                "type": "object",
                                "title": "通知设置",
                                "properties": {
                                    "price_change_alert": {
                                        "type": "boolean",
                                        "title": "价格变动提醒",
                                        "default": True
                                    },
                                    "competitor_alert": {
                                        "type": "boolean", 
                                        "title": "竞品动态提醒",
                                        "default": True
                                    },
                                    "inventory_alert": {
                                        "type": "boolean",
                                        "title": "库存预警",
                                        "default": True
                                    }
                                }
                            }
                        },
                        "required": ["enable_auto_pricing", "enable_competitor_monitoring"]
                    },
                    features=PLUGIN_INFO["features"]
                )
                
                db.add(plugin)
                await db.commit()
                await db.refresh(plugin)
                
                logger.info(f"插件 {PLUGIN_INFO['name']} 注册成功")
                
                # 创建插件相关的数据库表
                await create_plugin_tables(db)
                
            else:
                # 更新插件信息
                plugin.description = PLUGIN_INFO["description"]
                plugin.version = PLUGIN_INFO["version"]
                plugin.features = PLUGIN_INFO["features"]
                plugin.updated_at = datetime.now()
                
                await db.commit()
                logger.info(f"插件 {PLUGIN_INFO['name']} 信息已更新")
                
            return plugin
            
    except Exception as e:
        logger.error(f"初始化插件失败: {e}")
        raise

async def create_plugin_tables(db: AsyncSession):
    """创建插件相关的数据库表"""
    try:
        # 创建渠道配置表
        await db.execute(text("""
            CREATE TABLE IF NOT EXISTS multi_channel_configs (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
                channel_id UUID NOT NULL REFERENCES sales_channels(id) ON DELETE CASCADE,
                platform_config JSONB DEFAULT '{}',
                api_credentials JSONB DEFAULT '{}',
                sync_settings JSONB DEFAULT '{}',
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(project_id, channel_id)
            )
        """))
        
        # 创建商品映射表
        await db.execute(text("""
            CREATE TABLE IF NOT EXISTS multi_channel_product_mappings (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
                product_id UUID NOT NULL,
                channel_id UUID NOT NULL REFERENCES sales_channels(id) ON DELETE CASCADE,
                external_product_id VARCHAR(255),
                channel_product_data JSONB DEFAULT '{}',
                sync_status VARCHAR(50) DEFAULT 'pending',
                last_sync_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(project_id, product_id, channel_id)
            )
        """))
        
        # 创建竞品监控表
        await db.execute(text("""
            CREATE TABLE IF NOT EXISTS competitor_products (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
                competitor_name VARCHAR(255) NOT NULL,
                product_name VARCHAR(255) NOT NULL,
                channel_platform VARCHAR(100) NOT NULL,
                external_url TEXT,
                current_price DECIMAL(10,2),
                price_history JSONB DEFAULT '[]',
                product_data JSONB DEFAULT '{}',
                monitoring_status VARCHAR(50) DEFAULT 'active',
                last_monitored_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # 创建自动化规则表
        await db.execute(text("""
            CREATE TABLE IF NOT EXISTS automation_rules (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
                rule_name VARCHAR(255) NOT NULL,
                rule_type VARCHAR(100) NOT NULL,
                conditions JSONB NOT NULL DEFAULT '{}',
                actions JSONB NOT NULL DEFAULT '{}',
                is_active BOOLEAN DEFAULT true,
                execution_count INTEGER DEFAULT 0,
                last_executed_at TIMESTAMP WITH TIME ZONE,
                created_by UUID REFERENCES users(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # 创建操作日志表
        await db.execute(text("""
            CREATE TABLE IF NOT EXISTS multi_channel_operation_logs (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
                operation_type VARCHAR(100) NOT NULL,
                target_type VARCHAR(100) NOT NULL,
                target_id UUID,
                operation_data JSONB DEFAULT '{}',
                result_status VARCHAR(50) NOT NULL,
                result_message TEXT,
                executed_by UUID REFERENCES users(id),
                executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        await db.commit()
        logger.info("全渠道运营助手插件数据库表创建成功")
        
    except Exception as e:
        logger.error(f"创建插件数据库表失败: {e}")
        await db.rollback()
        raise

if __name__ == "__main__":
    import asyncio
    asyncio.run(init_plugin())
