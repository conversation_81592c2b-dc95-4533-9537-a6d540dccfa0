#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, desc, asc, and_, or_, text, case, select, cast, Date, Float
from sqlalchemy.orm import joinedload, selectinload
from collections import defaultdict

from models.project import Project
from models.user import User
from models.project_user import ProjectUser
from models.store import Store
from models.sales_report import SalesReport, SalesReportItem, SalesReportChannel
from models.sales_management import SalesChannel, PlatformService, ChannelPlatform
from models.warehouse import Warehouse
from models.inventory import InventoryItem
from models.purchase_order import PurchaseOrder, PurchaseOrderItem
from models.supplier import Supplier
from models.product import Product, ProductCategory
from models.finance import FinancialReconciliation
from models.ai import AIUsage
from models.notification import Notification
from services.ai.chat_service import AIChatService
from schemas.dashboard import SalesOverTime, SalesByCategory, PaymentDistribution, ChannelSales
from models.operation_log import OperationLog
from models.store_operations import StoreSalesTarget
from models.loss import Loss

logger = logging.getLogger(__name__)

class DashboardService:
    @staticmethod
    async def get_project_dashboard(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """获取项目管理仪表盘数据"""
        # 获取项目信息
        project_query = select(Project).where(Project.id == project_id)
        project_result = await db.execute(project_query)
        project = project_result.scalars().first()

        if not project:
            raise ValueError(f"项目不存在: {project_id}")

        # 获取用户数量
        users_query = select(func.count(ProjectUser.id)).where(ProjectUser.project_id == project_id)
        users_result = await db.execute(users_query)
        total_users = users_result.scalar() or 0

        # 获取门店数量
        stores_query = select(func.count(Store.id)).where(Store.project_id == project_id)
        stores_result = await db.execute(stores_query)
        total_stores = stores_result.scalar() or 0

        # 获取销售数据
        sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id)
        ).where(
            and_(
                SalesReport.project_id == project_id,
                SalesReport.report_date >= start_date,
                SalesReport.report_date <= end_date
            )
        )
        sales_result = await db.execute(sales_query)
        sales_row = sales_result.first()
        total_sales = sales_row[0] or 0
        total_orders = sales_row[1] or 0

        # 获取AI使用量
        ai_usage_query = select(func.count(AIUsage.id)).where(
            and_(
                AIUsage.project_id == project_id,
                AIUsage.created_at >= start_date,
                AIUsage.created_at <= end_date
            )
        )
        ai_usage_result = await db.execute(ai_usage_query)
        ai_usage = ai_usage_result.scalar() or 0

        # 计算增长率（与上一个时间段相比）
        previous_start = start_date - (end_date - start_date)
        previous_end = start_date - timedelta(seconds=1)

        # 上一时间段销售数据
        prev_sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id)
        ).where(
            and_(
                SalesReport.project_id == project_id,
                SalesReport.report_date >= previous_start,
                SalesReport.report_date <= previous_end
            )
        )
        prev_sales_result = await db.execute(prev_sales_query)
        prev_sales_row = prev_sales_result.first()
        prev_total_sales = prev_sales_row[0] or 0.01  # 避免除以零
        prev_total_orders = prev_sales_row[1] or 0.01  # 避免除以零

        # 计算增长率
        sales_growth = ((total_sales - prev_total_sales) / prev_total_sales) * 100
        order_growth = ((total_orders - prev_total_orders) / prev_total_orders) * 100

        # 获取今日数据
        today_start = datetime.combine(datetime.now().date(), datetime.min.time())
        today_end = datetime.combine(datetime.now().date(), datetime.max.time())
        yesterday_start = today_start - timedelta(days=1)
        yesterday_end = today_end - timedelta(days=1)

        # 今日销售数据
        today_sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id)
        ).where(
            and_(
                SalesReport.project_id == project_id,
                SalesReport.report_date >= today_start,
                SalesReport.report_date <= today_end
            )
        )
        today_sales_result = await db.execute(today_sales_query)
        today_sales_row = today_sales_result.first()
        today_sales_value = today_sales_row[0] or 0
        today_orders_value = today_sales_row[1] or 0

        # 昨日销售数据
        yesterday_sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id)
        ).where(
            and_(
                SalesReport.project_id == project_id,
                SalesReport.report_date >= yesterday_start,
                SalesReport.report_date <= yesterday_end
            )
        )
        yesterday_sales_result = await db.execute(yesterday_sales_query)
        yesterday_sales_row = yesterday_sales_result.first()
        yesterday_sales_value = yesterday_sales_row[0] or 0.01  # 避免除以零
        yesterday_orders_value = yesterday_sales_row[1] or 0.01  # 避免除以零

        # 计算今日变化率
        today_sales_change = ((today_sales_value - yesterday_sales_value) / yesterday_sales_value) * 100
        today_orders_change = ((today_orders_value - yesterday_orders_value) / yesterday_orders_value) * 100

        # 获取销售趋势数据（按日期）
        sales_trend_query = select(
            func.date_trunc('day', SalesReport.report_date).label('date'),
            func.sum(SalesReport.total_sales).label('value')
        ).where(
            and_(
                SalesReport.project_id == project_id,
                SalesReport.report_date >= start_date,
                SalesReport.report_date <= end_date
            )
        ).group_by(text('date')).order_by(text('date'))

        sales_trend_result = await db.execute(sales_trend_query)
        sales_trend_rows = sales_trend_result.all()

        sales_trend = [
            {
                "date": row.date.strftime('%Y-%m-%d'),
                "value": float(row.value)
            }
            for row in sales_trend_rows
        ]

        # 获取类别分布数据
        category_query = select(
            ProductCategory.name,
            func.sum(SalesReport.total_sales).label('value')
        ).join(
            Product, Product.category_id == ProductCategory.id
        ).join(
            SalesReport, SalesReport.project_id == Product.project_id
        ).where(
            and_(
                SalesReport.project_id == project_id,
                SalesReport.report_date >= start_date,
                SalesReport.report_date <= end_date
            )
        ).group_by(ProductCategory.name)

        category_result = await db.execute(category_query)
        category_rows = category_result.all()

        # 计算总销售额用于百分比计算
        total_category_sales = sum(row.value for row in category_rows) or 1  # 避免除以零

        category_distribution = [
            {
                "name": row.name,
                "value": float(row.value),
                "percentage": (float(row.value) / total_category_sales) * 100
            }
            for row in category_rows
        ]

        # 获取门店性能数据
        store_query = select(
            Store.id,
            Store.name,
            func.sum(SalesReport.total_sales).label('sales'),
            func.count(SalesReport.id).label('orders')
        ).join(
            SalesReport, SalesReport.store_id == Store.id
        ).where(
            and_(
                SalesReport.project_id == project_id,
                SalesReport.report_date >= start_date,
                SalesReport.report_date <= end_date
            )
        ).group_by(Store.id, Store.name)

        store_result = await db.execute(store_query)
        store_rows = store_result.all()

        store_performance = [
            {
                "id": row.id,
                "name": row.name,
                "sales": float(row.sales),
                "orders": row.orders
            }
            for row in store_rows
        ]

        # 获取最近活动（简化版，实际应从活动日志表获取）
        recent_activities = [
            {
                "id": str(uuid.uuid4()),
                "type": "user_login",
                "user_name": "系统管理员",
                "timestamp": datetime.now() - timedelta(minutes=30),
                "description": "系统管理员登录了系统"
            },
            {
                "id": str(uuid.uuid4()),
                "type": "sales_report",
                "user_name": "销售经理",
                "timestamp": datetime.now() - timedelta(hours=2),
                "description": "销售经理提交了销售日报"
            }
        ]

        # 获取AI推荐
        ai_recommendations = await DashboardService.get_dashboard_ai_recommendations(
            db=db,
            project_id=project_id,
            dashboard_type="project"
        )

        # 获取库存预警数据
        inventory_alerts_query = select(
            Product.id,
            Product.name,
            Product.unit,
            Warehouse.id.label('warehouse_id'),
            Warehouse.name.label('warehouse_name'),
            InventoryItem.quantity.label('current_quantity'),
            InventoryItem.min_quantity
        ).join(
            InventoryItem, InventoryItem.product_id == Product.id
        ).join(
            Warehouse, Warehouse.id == InventoryItem.warehouse_id
        ).where(
            and_(
                Product.project_id == project_id,
                InventoryItem.quantity <= InventoryItem.min_quantity
            )
        ).order_by(
            (InventoryItem.quantity / InventoryItem.min_quantity).asc()
        ).limit(20)

        inventory_alerts_result = await db.execute(inventory_alerts_query)
        inventory_alerts_rows = inventory_alerts_result.all()

        inventory_alerts = [
            {
                "id": row.id,
                "product_name": row.name,
                "unit": row.unit,
                "warehouse_id": row.warehouse_id,
                "warehouse_name": row.warehouse_name,
                "current_quantity": row.current_quantity,
                "min_quantity": row.min_quantity
            }
            for row in inventory_alerts_rows
        ]

        # 获取滞销商品数据
        # 使用is_slow_moving字段来标识滞销商品
        slow_moving_query = select(
            Product.id,
            Product.name,
            Warehouse.id.label('warehouse_id'),
            Warehouse.name.label('warehouse_name'),
            InventoryItem.quantity.label('current_quantity'),
            InventoryItem.is_slow_moving,
            InventoryItem.last_movement_date
        ).join(
            InventoryItem, InventoryItem.product_id == Product.id
        ).join(
            Warehouse, Warehouse.id == InventoryItem.warehouse_id
        ).where(
            and_(
                Product.project_id == project_id,
                InventoryItem.quantity > 0,
                InventoryItem.is_slow_moving == True
            )
        ).limit(20)

        slow_moving_result = await db.execute(slow_moving_query)
        slow_moving_rows = slow_moving_result.all()

        # 计算滞销天数
        now = datetime.now()
        slow_moving = []

        for row in slow_moving_rows:
            # 计算滞销天数，如果有last_movement_date则使用它，否则默认30天
            last_sold_days = 30
            if row.last_movement_date:
                last_sold_days = (now - row.last_movement_date).days

            slow_moving.append({
                "id": row.id,
                "product_name": row.name,
                "warehouse_id": row.warehouse_id,
                "warehouse_name": row.warehouse_name,
                "current_quantity": row.current_quantity,
                "last_sold_days": last_sold_days
            })

        # 获取通知列表
        notifications_query = select(
            Notification.id,
            Notification.title,
            Notification.content,
            Notification.type,
            Notification.created_at
        ).where(
            and_(
                Notification.project_id == project_id,
                Notification.is_read == False
            )
        ).order_by(
            desc(Notification.created_at)
        ).limit(10)

        try:
            notifications_result = await db.execute(notifications_query)
            notifications_rows = notifications_result.all()

            notifications = [
                {
                    "id": row.id,
                    "title": row.title,
                    "content": row.content,
                    "type": row.type,
                    "created_at": row.created_at
                }
                for row in notifications_rows
            ]
        except Exception as e:
            logger.error(f"获取通知列表失败: {e}")
            # 使用模拟数据
            notifications = [
                {
                    "id": str(uuid.uuid4()),
                    "title": "系统更新通知",
                    "content": "系统将于今晚22:00-23:00进行例行维护，请提前做好准备。",
                    "type": "info",
                    "created_at": datetime.now() - timedelta(hours=2)
                },
                {
                    "id": str(uuid.uuid4()),
                    "title": "库存预警",
                    "content": "商品「热销商品A」库存不足，请及时补货。",
                    "type": "warning",
                    "created_at": datetime.now() - timedelta(hours=5)
                }
            ]

        # 构建响应数据
        return {
            "last_updated": datetime.now(),
            "total_sales": float(total_sales),
            "total_orders": total_orders,
            "total_users": total_users,
            "total_stores": total_stores,
            "ai_usage": ai_usage,
            "sales_growth": float(sales_growth),
            "order_growth": float(order_growth),
            "user_growth": 0.0,  # 需要实际计算

            "today_sales": {
                "value": float(today_sales_value),
                "change": float(today_sales_change),
                "is_increase": today_sales_change >= 0
            },
            "today_orders": {
                "value": today_orders_value,
                "change": float(today_orders_change),
                "is_increase": today_orders_change >= 0
            },
            "today_users": {
                "value": 0,  # 需要实际计算
                "change": 0.0,
                "is_increase": True
            },
            "today_ai_usage": {
                "value": 0,  # 需要实际计算
                "change": 0.0,
                "is_increase": True
            },

            "sales_trend": sales_trend,
            "category_distribution": category_distribution,
            "store_performance": store_performance,
            "recent_activities": recent_activities,
            "inventory_alerts": inventory_alerts,
            "slow_moving": slow_moving,
            "notifications": notifications,
            "ai_recommendations": ai_recommendations
        }

    @staticmethod
    async def get_operations_dashboard(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取运营仪表盘数据"""
        # 构建基础查询条件
        base_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        ]

        # 如果指定了门店，添加门店过滤条件
        if store_id:
            base_conditions.append(SalesReport.store_id == store_id)

        # 获取销售数据
        sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id),
            func.sum(SalesReport.total_customers)
        ).where(and_(*base_conditions))

        sales_result = await db.execute(sales_query)
        sales_row = sales_result.first()
        total_sales = sales_row[0] or 0
        total_orders = sales_row[1] or 0
        total_customers = sales_row[2] or 0

        # 计算平均订单金额
        average_order_value = total_sales / total_orders if total_orders > 0 else 0

        # 计算增长率（与上一个时间段相比）
        previous_start = start_date - (end_date - start_date)
        previous_end = start_date - timedelta(seconds=1)

        # 构建上一时间段查询条件
        prev_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= previous_start,
            SalesReport.report_date <= previous_end
        ]

        if store_id:
            prev_conditions.append(SalesReport.store_id == store_id)

        # 上一时间段销售数据
        prev_sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id),
            func.sum(SalesReport.total_customers)
        ).where(and_(*prev_conditions))

        prev_sales_result = await db.execute(prev_sales_query)
        prev_sales_row = prev_sales_result.first()
        prev_total_sales = prev_sales_row[0] or 0.01  # 避免除以零
        prev_total_orders = prev_sales_row[1] or 0.01  # 避免除以零
        prev_total_customers = prev_sales_row[2] or 0.01  # 避免除以零

        # 计算增长率
        sales_growth = ((total_sales - prev_total_sales) / prev_total_sales) * 100
        order_growth = ((total_orders - prev_total_orders) / prev_total_orders) * 100
        customer_growth = ((total_customers - prev_total_customers) / prev_total_customers) * 100

        # 获取今日数据
        today_start = datetime.combine(datetime.now().date(), datetime.min.time())
        today_end = datetime.combine(datetime.now().date(), datetime.max.time())
        yesterday_start = today_start - timedelta(days=1)
        yesterday_end = today_end - timedelta(days=1)

        # 构建今日查询条件
        today_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= today_start,
            SalesReport.report_date <= today_end
        ]

        if store_id:
            today_conditions.append(SalesReport.store_id == store_id)

        # 今日销售数据
        today_sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id),
            func.sum(SalesReport.total_customers)
        ).where(and_(*today_conditions))

        today_sales_result = await db.execute(today_sales_query)
        today_sales_row = today_sales_result.first()
        today_sales_value = today_sales_row[0] or 0
        today_orders_value = today_sales_row[1] or 0
        today_customers_value = today_sales_row[2] or 0

        # 计算今日平均订单金额
        today_avg_order = today_sales_value / today_orders_value if today_orders_value > 0 else 0

        # 构建昨日查询条件
        yesterday_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= yesterday_start,
            SalesReport.report_date <= yesterday_end
        ]

        if store_id:
            yesterday_conditions.append(SalesReport.store_id == store_id)

        # 昨日销售数据
        yesterday_sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id),
            func.sum(SalesReport.total_customers)
        ).where(and_(*yesterday_conditions))

        yesterday_sales_result = await db.execute(yesterday_sales_query)
        yesterday_sales_row = yesterday_sales_result.first()
        yesterday_sales_value = yesterday_sales_row[0] or 0.01  # 避免除以零
        yesterday_orders_value = yesterday_sales_row[1] or 0.01  # 避免除以零
        yesterday_customers_value = yesterday_sales_row[2] or 0.01  # 避免除以零
        yesterday_avg_order = yesterday_sales_value / yesterday_orders_value if yesterday_orders_value > 0 else 0.01

        # 计算今日变化率
        today_sales_change = ((today_sales_value - yesterday_sales_value) / yesterday_sales_value) * 100
        today_orders_change = ((today_orders_value - yesterday_orders_value) / yesterday_orders_value) * 100
        today_customers_change = ((today_customers_value - yesterday_customers_value) / yesterday_customers_value) * 100
        today_avg_order_change = ((today_avg_order - yesterday_avg_order) / yesterday_avg_order) * 100

        # 获取销售趋势数据（按日期）
        sales_trend_query = select(
            func.date_trunc('day', SalesReport.report_date).label('date'),
            func.sum(SalesReport.total_sales).label('value')
        ).where(and_(*base_conditions)).group_by(text('date')).order_by(text('date'))

        sales_trend_result = await db.execute(sales_trend_query)
        sales_trend_rows = sales_trend_result.all()

        sales_trend = [
            {
                "date": row.date.strftime('%Y-%m-%d'),
                "value": float(row.value)
            }
            for row in sales_trend_rows
        ]

        # 获取类别分布数据
        category_query = select(
            ProductCategory.name,
            func.sum(SalesReport.total_sales).label('value')
        ).join(
            Product, Product.category_id == ProductCategory.id
        ).join(
            SalesReport, SalesReport.project_id == Product.project_id
        ).where(and_(*base_conditions)).group_by(ProductCategory.name)

        category_result = await db.execute(category_query)
        category_rows = category_result.all()

        # 计算总销售额用于百分比计算
        total_category_sales = sum(row.value for row in category_rows) or 1  # 避免除以零

        category_distribution = [
            {
                "name": row.name,
                "value": float(row.value),
                "percentage": (float(row.value) / total_category_sales) * 100
            }
            for row in category_rows
        ]

        # 获取支付方式分布数据
        # 注意：这里假设SalesReport.payment_methods是一个JSONB字段，存储支付方式统计
        # 实际实现可能需要根据数据库结构调整
        payment_distribution = []

        # 获取门店性能数据
        store_query = select(
            Store.id,
            Store.name,
            func.sum(SalesReport.total_sales).label('sales'),
            func.count(SalesReport.id).label('orders'),
            func.sum(SalesReport.total_customers).label('customers')
        ).join(
            SalesReport, SalesReport.store_id == Store.id
        ).where(and_(*base_conditions)).group_by(Store.id, Store.name)

        store_result = await db.execute(store_query)
        store_rows = store_result.all()

        store_performance = [
            {
                "id": row.id,
                "name": row.name,
                "sales": float(row.sales),
                "orders": row.orders,
                "customers": row.customers
            }
            for row in store_rows
        ]

        # 获取产品性能数据
        product_query = select(
            Product.id,
            Product.name,
            func.sum(SalesReport.total_sales).label('sales'),
            func.sum(SalesReport.total_orders).label('quantity')
        ).join(
            SalesReport, SalesReport.project_id == Product.project_id
        ).where(and_(*base_conditions)).group_by(Product.id, Product.name).order_by(desc('sales')).limit(10)

        product_result = await db.execute(product_query)
        product_rows = product_result.all()

        product_performance = [
            {
                "id": row.id,
                "name": row.name,
                "sales": float(row.sales),
                "quantity": row.quantity
            }
            for row in product_rows
        ]

        # 获取分时段销售数据
        # 注意：这里假设SalesReport.hourly_sales是一个JSONB字段，存储分时段销售统计
        # 实际实现可能需要根据数据库结构调整
        hourly_sales = {}

        # 获取渠道销售数据 - 直接从销售上报的channel_sales字段获取
        channel_query = select(
            SalesChannel.custom_name,
            func.sum(SalesReportChannel.total_sales).label("total_channel_sales")
        ).select_from(SalesReport).join(
            SalesReportChannel, SalesReport.id == SalesReportChannel.sales_report_id
        ).join(
            SalesChannel, SalesReportChannel.sales_channel_id == SalesChannel.id
        ).where(and_(*base_conditions)).group_by(SalesChannel.custom_name)

        channel_result = await db.execute(channel_query)
        channel_sales_data = channel_result.all()

        channel_sales = [
            ChannelSales(name=row.name, value=row.total_channel_sales) for row in channel_sales_data
        ]

        # 获取充值售卡数据
        # 使用 base_conditions 确保应用门店过滤条件
        recharge_query = select(
            func.sum(SalesReport.recharge_amount).label('recharge_amount'),
            func.sum(SalesReport.card_sales_amount).label('card_sales_amount'),
            func.sum(SalesReport.recharge_count).label('recharge_count'),
            func.sum(SalesReport.card_sales_count).label('card_sales_count')
        ).where(and_(*base_conditions))

        recharge_result = await db.execute(recharge_query)
        recharge_row = recharge_result.first()

        # 创建充值售卡数据
        recharge_data = {
            "recharge_amount": float(recharge_row.recharge_amount or 0),
            "card_sales_amount": float(recharge_row.card_sales_amount or 0),
            "recharge_count": int(recharge_row.recharge_count or 0),
            "card_sales_count": int(recharge_row.card_sales_count or 0)
        }

        # 获取AI推荐
        ai_recommendations = await DashboardService.get_dashboard_ai_recommendations(
            db=db,
            project_id=project_id,
            dashboard_type="operations"
        )

        # 构建响应数据
        return {
            "last_updated": datetime.now(),
            "total_sales": float(total_sales),
            "total_orders": total_orders,
            "total_customers": total_customers,
            "average_order_value": float(average_order_value),
            "sales_growth": float(sales_growth),
            "order_growth": float(order_growth),
            "customer_growth": float(customer_growth),

            "today_sales": {
                "value": float(today_sales_value),
                "change": float(today_sales_change),
                "is_increase": today_sales_change >= 0
            },
            "today_orders": {
                "value": today_orders_value,
                "change": float(today_orders_change),
                "is_increase": today_orders_change >= 0
            },
            "today_customers": {
                "value": today_customers_value,
                "change": float(today_customers_change),
                "is_increase": today_customers_change >= 0
            },
            "today_average_order": {
                "value": float(today_avg_order),
                "change": float(today_avg_order_change),
                "is_increase": today_avg_order_change >= 0
            },

            "sales_trend": sales_trend,
            "category_distribution": category_distribution,
            "payment_distribution": payment_distribution,
            "store_performance": store_performance,
            "product_performance": product_performance,
            "hourly_sales": hourly_sales,
            "channel_sales": channel_sales,
            "recharge_data": recharge_data,
            "ai_recommendations": ai_recommendations
        }

    @staticmethod
    async def get_finance_dashboard(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取财务仪表盘数据"""
        # 构建基础查询条件
        base_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        ]

        # 如果指定了门店，添加门店过滤条件
        if store_id:
            base_conditions.append(SalesReport.store_id == store_id)

        # 获取销售数据（收入）
        revenue_query = select(
            func.sum(SalesReport.total_sales)
        ).where(and_(*base_conditions))

        revenue_result = await db.execute(revenue_query)
        total_revenue = revenue_result.scalar() or 0

        # 获取采购数据（成本）- 简化版，实际应从采购订单或成本记录中获取
        # 这里假设成本是收入的60%
        total_cost = total_revenue * 0.6

        # 计算毛利润和利润率
        gross_profit = total_revenue - total_cost
        profit_margin = (gross_profit / total_revenue) * 100 if total_revenue > 0 else 0

        # 计算增长率（与上一个时间段相比）
        previous_start = start_date - (end_date - start_date)
        previous_end = start_date - timedelta(seconds=1)

        # 构建上一时间段查询条件
        prev_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= previous_start,
            SalesReport.report_date <= previous_end
        ]

        if store_id:
            prev_conditions.append(SalesReport.store_id == store_id)

        # 上一时间段销售数据
        prev_revenue_query = select(
            func.sum(SalesReport.total_sales)
        ).where(and_(*prev_conditions))

        prev_revenue_result = await db.execute(prev_revenue_query)
        prev_total_revenue = prev_revenue_result.scalar() or 0.01  # 避免除以零

        # 上一时间段成本和利润
        prev_total_cost = prev_total_revenue * 0.6
        prev_gross_profit = prev_total_revenue - prev_total_cost

        # 计算增长率
        revenue_growth = ((total_revenue - prev_total_revenue) / prev_total_revenue) * 100
        cost_growth = ((total_cost - prev_total_cost) / prev_total_cost) * 100
        profit_growth = ((gross_profit - prev_gross_profit) / prev_gross_profit) * 100 if prev_gross_profit > 0 else 0

        # 获取当月数据
        month_start = datetime(end_date.year, end_date.month, 1)
        month_end = end_date
        prev_month_end = month_start - timedelta(days=1)
        prev_month_start = datetime(prev_month_end.year, prev_month_end.month, 1)

        # 构建当月查询条件
        month_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= month_start,
            SalesReport.report_date <= month_end
        ]

        if store_id:
            month_conditions.append(SalesReport.store_id == store_id)

        # 当月销售数据
        month_revenue_query = select(
            func.sum(SalesReport.total_sales)
        ).where(and_(*month_conditions))

        month_revenue_result = await db.execute(month_revenue_query)
        month_revenue = month_revenue_result.scalar() or 0

        # 当月成本和利润
        month_cost = month_revenue * 0.6
        month_profit = month_revenue - month_cost
        month_margin = (month_profit / month_revenue) * 100 if month_revenue > 0 else 0

        # 构建上月查询条件
        prev_month_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= prev_month_start,
            SalesReport.report_date <= prev_month_end
        ]

        if store_id:
            prev_month_conditions.append(SalesReport.store_id == store_id)

        # 上月销售数据
        prev_month_revenue_query = select(
            func.sum(SalesReport.total_sales)
        ).where(and_(*prev_month_conditions))

        prev_month_revenue_result = await db.execute(prev_month_revenue_query)
        prev_month_revenue = prev_month_revenue_result.scalar() or 0.01  # 避免除以零

        # 上月成本和利润
        prev_month_cost = prev_month_revenue * 0.6
        prev_month_profit = prev_month_revenue - prev_month_cost
        prev_month_margin = (prev_month_profit / prev_month_revenue) * 100 if prev_month_revenue > 0 else 0

        # 计算月度变化率
        month_revenue_change = ((month_revenue - prev_month_revenue) / prev_month_revenue) * 100
        month_cost_change = ((month_cost - prev_month_cost) / prev_month_cost) * 100
        month_profit_change = ((month_profit - prev_month_profit) / prev_month_profit) * 100 if prev_month_profit > 0 else 0
        month_margin_change = month_margin - prev_month_margin

        # 获取收入趋势数据（按日期）
        revenue_trend_query = select(
            func.date_trunc('day', SalesReport.report_date).label('date'),
            func.sum(SalesReport.total_sales).label('value')
        ).where(and_(*base_conditions)).group_by(text('date')).order_by(text('date'))

        revenue_trend_result = await db.execute(revenue_trend_query)
        revenue_trend_rows = revenue_trend_result.all()

        revenue_trend = [
            {
                "date": row.date.strftime('%Y-%m-%d'),
                "value": float(row.value)
            }
            for row in revenue_trend_rows
        ]

        # 成本趋势（基于收入计算）
        cost_trend = [
            {
                "date": item["date"],
                "value": float(item["value"]) * 0.6
            }
            for item in revenue_trend
        ]

        # 利润趋势（基于收入和成本计算）
        profit_trend = [
            {
                "date": item["date"],
                "value": float(item["value"]) * 0.4
            }
            for item in revenue_trend
        ]

        # 获取类别利润数据
        category_query = select(
            ProductCategory.name,
            func.sum(SalesReport.total_sales).label('value')
        ).join(
            Product, Product.category_id == ProductCategory.id
        ).join(
            SalesReport, SalesReport.project_id == Product.project_id
        ).where(and_(*base_conditions)).group_by(ProductCategory.name)

        category_result = await db.execute(category_query)
        category_rows = category_result.all()

        # 计算总销售额用于百分比计算
        total_category_sales = sum(row.value for row in category_rows) or 1  # 避免除以零

        category_profit = [
            {
                "name": row.name,
                "value": float(row.value) * 0.4,  # 假设利润是销售额的40%
                "percentage": (float(row.value) / total_category_sales) * 100
            }
            for row in category_rows
        ]

        # 获取门店利润数据
        store_query = select(
            Store.id,
            Store.name,
            func.sum(SalesReport.total_sales).label('sales'),
            func.count(SalesReport.id).label('orders')  # 添加订单数量
        ).join(
            SalesReport, SalesReport.store_id == Store.id
        ).where(and_(*base_conditions)).group_by(Store.id, Store.name)

        store_result = await db.execute(store_query)
        store_rows = store_result.all()

        store_profit = [
            {
                "id": row.id,
                "name": row.name,
                "sales": float(row.sales),
                "profit": float(row.sales) * 0.4,  # 假设利润是销售额的40%
                "orders": row.orders  # 添加订单数量
            }
            for row in store_rows
        ]

        # 获取支付对账数据
        # 注意：这里简化处理，实际应从财务对账表中获取
        payment_reconciliation = {
            "total_reported": float(total_revenue),
            "total_system": float(total_revenue) * 0.98,  # 假设系统记录的销售额与报告的有2%的差异
            "difference": float(total_revenue) * 0.02,
            "difference_percentage": 2.0,
            "status": "normal",
            "channels": [
                {
                    "name": "线下销售",
                    "reported": float(total_revenue) * 0.7,
                    "system": float(total_revenue) * 0.68,
                    "difference": float(total_revenue) * 0.02
                },
                {
                    "name": "线上销售",
                    "reported": float(total_revenue) * 0.3,
                    "system": float(total_revenue) * 0.3,
                    "difference": 0
                }
            ]
        }

        # 获取产品利润数据
        product_query = select(
            Product.id,
            Product.name,
            func.sum(SalesReport.total_sales).label('sales')
        ).join(
            SalesReport, SalesReport.project_id == Product.project_id
        ).where(and_(*base_conditions)).group_by(Product.id, Product.name).order_by(desc('sales')).limit(10)

        product_result = await db.execute(product_query)
        product_rows = product_result.all()

        profit_by_product = [
            {
                "id": row.id,
                "name": row.name,
                "sales": float(row.sales),
                "cost": float(row.sales) * 0.6,  # 假设成本是销售额的60%
                "profit": float(row.sales) * 0.4,  # 假设利润是销售额的40%
                "margin": 40.0  # 假设利润率是40%
            }
            for row in product_rows
        ]

        # 获取AI推荐
        ai_recommendations = await DashboardService.get_dashboard_ai_recommendations(
            db=db,
            project_id=project_id,
            dashboard_type="finance"
        )

        # 构建响应数据
        return {
            "last_updated": datetime.now(),
            "total_revenue": float(total_revenue),
            "total_cost": float(total_cost),
            "gross_profit": float(gross_profit),
            "profit_margin": float(profit_margin),
            "revenue_growth": float(revenue_growth),
            "cost_growth": float(cost_growth),
            "profit_growth": float(profit_growth),

            "monthly_revenue": {
                "value": float(month_revenue),
                "change": float(month_revenue_change),
                "is_increase": month_revenue_change >= 0
            },
            "monthly_cost": {
                "value": float(month_cost),
                "change": float(month_cost_change),
                "is_increase": month_cost_change <= 0  # 成本下降是好事
            },
            "monthly_profit": {
                "value": float(month_profit),
                "change": float(month_profit_change),
                "is_increase": month_profit_change >= 0
            },
            "monthly_margin": {
                "value": float(month_margin),
                "change": float(month_margin_change),
                "is_increase": month_margin_change >= 0
            },

            "revenue_trend": revenue_trend,
            "cost_trend": cost_trend,
            "profit_trend": profit_trend,
            "category_profit": category_profit,
            "store_profit": store_profit,
            "payment_reconciliation": payment_reconciliation,
            "profit_by_product": profit_by_product,
            "ai_recommendations": ai_recommendations
        }

    @staticmethod
    async def get_warehouse_dashboard(
        db: AsyncSession,
        project_id: uuid.UUID,
        warehouse_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取仓储仪表盘数据"""
        # 构建基础查询条件
        base_conditions = [
            InventoryItem.project_id == project_id
        ]

        # 如果指定了仓库，添加仓库过滤条件
        if warehouse_id:
            base_conditions.append(InventoryItem.warehouse_id == warehouse_id)

        # 获取库存总量
        inventory_query = select(
            func.sum(InventoryItem.quantity)
        ).where(and_(*base_conditions))

        inventory_result = await db.execute(inventory_query)
        total_inventory = inventory_result.scalar() or 0

        # 获取库存总价值（简化版，实际应从产品成本和库存数量计算）
        # 这里假设每个库存项的价值是10元
        total_value = total_inventory * 10

        # 获取低库存商品数量
        low_stock_query = select(
            func.count(InventoryItem.id)
        ).where(
            and_(
                *base_conditions,
                InventoryItem.quantity <= InventoryItem.min_quantity
            )
        )

        low_stock_result = await db.execute(low_stock_query)
        low_stock_count = low_stock_result.scalar() or 0

        # 获取过剩库存商品数量
        excess_stock_query = select(
            func.count(InventoryItem.id)
        ).where(
            and_(
                *base_conditions,
                InventoryItem.max_quantity.isnot(None),
                InventoryItem.quantity >= InventoryItem.max_quantity
            )
        )

        excess_stock_result = await db.execute(excess_stock_query)
        excess_stock_count = excess_stock_result.scalar() or 0

        # 获取滞销商品数量
        slow_moving_query = select(
            func.count(InventoryItem.id)
        ).where(
            and_(
                *base_conditions,
                InventoryItem.is_slow_moving == True
            )
        )

        slow_moving_result = await db.execute(slow_moving_query)
        slow_moving_count = slow_moving_result.scalar() or 0

        # 计算库存周转率（简化版，实际应从销售和库存历史计算）
        # 这里假设周转率是3次/月
        turnover_rate = 3.0

        # 获取按仓库分类的库存数据
        warehouse_query = select(
            Warehouse.id,
            Warehouse.name,
            func.sum(InventoryItem.quantity).label('quantity')
        ).join(
            InventoryItem, InventoryItem.warehouse_id == Warehouse.id
        ).where(
            InventoryItem.project_id == project_id
        ).group_by(Warehouse.id, Warehouse.name)

        warehouse_result = await db.execute(warehouse_query)
        warehouse_rows = warehouse_result.all()

        inventory_by_warehouse = [
            {
                "id": row.id,
                "name": row.name,
                "quantity": row.quantity,
                "value": row.quantity * 10  # 假设每个库存项的价值是10元
            }
            for row in warehouse_rows
        ]

        # 获取按类别分类的库存数据
        category_query = select(
            ProductCategory.name,
            func.sum(InventoryItem.quantity).label('quantity')
        ).join(
            Product, Product.category_id == ProductCategory.id
        ).join(
            InventoryItem, InventoryItem.product_id == Product.id
        ).where(
            InventoryItem.project_id == project_id
        ).group_by(ProductCategory.name)

        category_result = await db.execute(category_query)
        category_rows = category_result.all()

        # 计算总库存量用于百分比计算
        total_category_inventory = sum(row.quantity for row in category_rows) or 1  # 避免除以零

        inventory_by_category = [
            {
                "name": row.name,
                "value": row.quantity,
                "percentage": (row.quantity / total_category_inventory) * 100
            }
            for row in category_rows
        ]

        # 获取库存预警数据
        alerts_query = select(
            InventoryItem.id,
            InventoryItem.product_id,
            Product.name.label('product_name'),
            Product.sku.label('product_code'),  # 使用 sku 替代 code
            InventoryItem.quantity.label('current_quantity'),
            InventoryItem.min_quantity.label('threshold_quantity'),
            InventoryItem.warehouse_id,
            Warehouse.name.label('warehouse_name'),
            InventoryItem.is_slow_moving,
            InventoryItem.last_movement_date
        ).join(
            Product, InventoryItem.product_id == Product.id
        ).join(
            Warehouse, InventoryItem.warehouse_id == Warehouse.id
        ).where(
            and_(
                InventoryItem.project_id == project_id,
                or_(
                    InventoryItem.quantity <= InventoryItem.min_quantity,
                    InventoryItem.is_slow_moving == True,
                    and_(
                        InventoryItem.max_quantity.isnot(None),
                        InventoryItem.quantity >= InventoryItem.max_quantity
                    )
                )
            )
        ).limit(50)

        alerts_result = await db.execute(alerts_query)
        alerts_rows = alerts_result.all()

        # 计算每个预警项的天数（如果是滞销品）
        now = datetime.now()

        inventory_alerts = []
        for row in alerts_rows:
            alert_type = "normal"
            days_without_movement = None
            suggested_action = None

            if row.quantity <= row.threshold_quantity:
                alert_type = "low_stock"
                suggested_action = "补货"
            elif row.is_slow_moving:
                alert_type = "slow_moving"
                if row.last_movement_date:
                    days_without_movement = (now - row.last_movement_date).days
                suggested_action = "促销或清仓"
            else:
                alert_type = "excess_stock"
                suggested_action = "调拨或促销"

            inventory_alerts.append({
                "id": row.id,
                "product_id": row.product_id,
                "product_name": row.product_name,
                "product_code": row.product_code,
                "current_quantity": row.current_quantity,
                "threshold_quantity": row.threshold_quantity,
                "warehouse_id": row.warehouse_id,
                "warehouse_name": row.warehouse_name,
                "alert_type": alert_type,
                "days_without_movement": days_without_movement,
                "suggested_action": suggested_action
            })

        # 获取最近库存变动（简化版，实际应从库存历史记录获取）
        recent_movements = [
            {
                "id": str(uuid.uuid4()),
                "product_name": "示例产品1",
                "warehouse_name": "主仓库",
                "action_type": "入库",
                "quantity": 100,
                "date": datetime.now() - timedelta(days=1),
                "operator": "仓库管理员"
            },
            {
                "id": str(uuid.uuid4()),
                "product_name": "示例产品2",
                "warehouse_name": "主仓库",
                "action_type": "出库",
                "quantity": 50,
                "date": datetime.now() - timedelta(days=2),
                "operator": "仓库管理员"
            }
        ]

        # 获取库存趋势数据（简化版，实际应从库存历史记录获取）
        inventory_trend = [
            {
                "date": (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'),
                "value": total_inventory - i * 10
            }
            for i in range(7)
        ]

        # 获取AI推荐
        ai_recommendations = await DashboardService.get_dashboard_ai_recommendations(
            db=db,
            project_id=project_id,
            dashboard_type="warehouse"
        )

        # 构建响应数据
        return {
            "last_updated": datetime.now(),
            "total_inventory": total_inventory,
            "total_value": float(total_value),
            "low_stock_count": low_stock_count,
            "excess_stock_count": excess_stock_count,
            "slow_moving_count": slow_moving_count,
            "turnover_rate": float(turnover_rate),

            "inventory_by_warehouse": inventory_by_warehouse,
            "inventory_by_category": inventory_by_category,
            "inventory_alerts": inventory_alerts,
            "recent_movements": recent_movements,
            "inventory_trend": inventory_trend,
            "ai_recommendations": ai_recommendations
        }

    @staticmethod
    async def get_store_dashboard(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取门店仪表盘数据"""
        # 构建基础查询条件
        base_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        ]

        # 如果指定了门店，添加门店过滤条件
        if store_id:
            base_conditions.append(SalesReport.store_id == store_id)

        # 获取销售数据
        sales_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id),
            func.sum(SalesReport.total_customers)
        ).where(and_(*base_conditions))

        sales_result = await db.execute(sales_query)
        sales_row = sales_result.first()

        total_sales = sales_row[0] or 0
        total_orders = sales_row[1] or 0
        total_customers = sales_row[2] or 0

        # 获取今日销售数据
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())

        today_conditions = base_conditions.copy()
        today_conditions.append(SalesReport.report_date >= today_start)
        today_conditions.append(SalesReport.report_date <= today_end)

        today_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id),
            func.sum(SalesReport.total_customers)
        ).where(and_(*today_conditions))

        today_result = await db.execute(today_query)
        today_row = today_result.first()

        today_sales = today_row[0] or 0
        today_orders = today_row[1] or 0
        today_customers = today_row[2] or 0

        # 获取昨日销售数据（用于计算变化率）
        yesterday = today - timedelta(days=1)
        yesterday_start = datetime.combine(yesterday, datetime.min.time())
        yesterday_end = datetime.combine(yesterday, datetime.max.time())

        yesterday_conditions = base_conditions.copy()
        yesterday_conditions.append(SalesReport.report_date >= yesterday_start)
        yesterday_conditions.append(SalesReport.report_date <= yesterday_end)

        yesterday_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id),
            func.sum(SalesReport.total_customers)
        ).where(and_(*yesterday_conditions))

        yesterday_result = await db.execute(yesterday_query)
        yesterday_row = yesterday_result.first()

        yesterday_sales = yesterday_row[0] or 0.01  # 避免除以零
        yesterday_orders = yesterday_row[1] or 0.01
        yesterday_customers = yesterday_row[2] or 0.01

        # 计算变化率
        sales_change = ((today_sales - yesterday_sales) / yesterday_sales) * 100
        orders_change = ((today_orders - yesterday_orders) / yesterday_orders) * 100
        customers_change = ((today_customers - yesterday_customers) / yesterday_customers) * 100

        # 获取销售趋势数据
        trend_query = select(
            func.date_trunc('day', SalesReport.report_date).label('report_date'),
            func.sum(SalesReport.total_sales).label('sales'),
            func.count(SalesReport.id).label('orders'),
            func.sum(SalesReport.total_customers).label('customers')
        ).where(
            and_(*base_conditions)
        ).group_by(
            text('report_date')
        ).order_by(
            text('report_date')
        )

        trend_result = await db.execute(trend_query)
        trend_rows = trend_result.all()

        sales_trend = []
        for row in trend_rows:
            if row.report_date:
                date_str = row.report_date.strftime('%Y-%m-%d')
                sales_value = float(row.sales) if row.sales is not None else 0.0
                sales_trend.append({
                    'date': date_str,
                    'value': sales_value,  # 添加value字段，与SalesTrendPoint模型匹配
                    'sales': sales_value,
                    'orders': row.orders if row.orders is not None else 0,
                    'customers': row.customers if row.customers is not None else 0
                })

        # 获取商品销售排行
        product_query = select(
            Product.id,
            Product.name,
            func.sum(SalesReportItem.quantity).label('quantity'),
            func.sum(SalesReportItem.total_amount).label('amount')
        ).join(
            SalesReportItem, SalesReportItem.product_id == Product.id
        ).join(
            SalesReport, SalesReport.id == SalesReportItem.sales_report_id
        ).where(
            and_(*base_conditions)
        ).group_by(
            Product.id, Product.name
        ).order_by(
            desc('amount')
        ).limit(10)

        product_result = await db.execute(product_query)
        product_rows = product_result.all()

        product_ranking = []
        for row in product_rows:
            product_ranking.append({
                'id': row.id,
                'name': row.name,
                'quantity': row.quantity,
                'amount': float(row.amount)
            })

        # 获取任务完成情况
        # 这里假设有一个任务表，实际实现可能需要根据数据库结构调整
        task_completion = [
            {'name': '日常清洁', 'completed': 12, 'total': 15},
            {'name': '商品上架', 'completed': 8, 'total': 10},
            {'name': '库存盘点', 'completed': 5, 'total': 5},
            {'name': '员工培训', 'completed': 3, 'total': 4}
        ]

        # 获取AI推荐
        ai_recommendations = [
            {
                'id': '1',
                'title': '增加促销活动',
                'content': '根据销售数据分析，建议在周末增加促销活动，提高客流量。',
                'type': '销售增长',
                'priority': '高',
                'action_items': [
                    '设计周末促销方案',
                    '准备促销商品',
                    '安排促销人员'
                ]
            },
            {
                'id': '2',
                'title': '优化商品陈列',
                'content': '热销商品应放在更显眼的位置，提高客户购买率。',
                'type': '运营优化',
                'priority': '中',
                'action_items': [
                    '调整商品陈列位置',
                    '增加热销商品库存'
                ]
            }
        ]

        # 计算增长率（与上周同期相比）
        # 获取上周同期数据
        last_week_start = start_date - timedelta(days=7)
        last_week_end = end_date - timedelta(days=7)

        last_week_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.report_date >= last_week_start,
            SalesReport.report_date <= last_week_end
        ]

        if store_id:
            last_week_conditions.append(SalesReport.store_id == store_id)

        last_week_query = select(
            func.sum(SalesReport.total_sales),
            func.count(SalesReport.id),
            func.sum(SalesReport.total_customers)
        ).where(and_(*last_week_conditions))

        last_week_result = await db.execute(last_week_query)
        last_week_row = last_week_result.first()

        last_week_sales = last_week_row[0] or 0.01  # 避免除以零
        last_week_orders = last_week_row[1] or 0.01
        last_week_customers = last_week_row[2] or 0.01

        # 计算增长率
        sales_growth = ((total_sales - last_week_sales) / last_week_sales) * 100
        order_growth = ((total_orders - last_week_orders) / last_week_orders) * 100
        customer_growth = ((total_customers - last_week_customers) / last_week_customers) * 100

        # 计算今日平均订单金额
        today_average_order_value = today_sales / today_orders if today_orders > 0 else 0
        yesterday_average_order_value = yesterday_sales / yesterday_orders if yesterday_orders > 0 else 0.01
        average_order_change = ((today_average_order_value - yesterday_average_order_value) / yesterday_average_order_value) * 100

        # 获取产品类别分布数据 - 直接从销售上报的product_categories字段获取
        category_query = select(
            SalesReport.product_categories
        ).where(
            and_(
                SalesReport.project_id == project_id,
                SalesReport.report_date >= start_date,
                SalesReport.report_date <= end_date,
                SalesReport.product_categories.is_not(None)
            )
        )

        category_result = await db.execute(category_query)
        category_rows = category_result.all()

        # 合并所有报告的类别数据
        category_data = {}
        for row in category_rows:
            if row.product_categories:
                for category, value in row.product_categories.items():
                    if category in category_data:
                        category_data[category] += float(value)
                    else:
                        category_data[category] = float(value)

        # 计算总销售额
        total_category_sales = sum(category_data.values()) if category_data else 0

        # 创建类别分布数据
        category_distribution = []
        for category, value in category_data.items():
            percentage = (value / total_category_sales * 100) if total_category_sales > 0 else 0
            category_distribution.append({
                "name": category,
                "value": float(value),
                "percentage": round(percentage)
            })

        # 获取支付方式分布数据 - 直接从销售上报的payment_methods_data字段获取
        # 使用 base_conditions 确保应用门店过滤条件
        payment_conditions = base_conditions.copy()
        payment_conditions.append(SalesReport.payment_methods_data.is_not(None))

        payment_query = select(
            SalesReport.payment_methods_data
        ).where(and_(*payment_conditions))

        payment_result = await db.execute(payment_query)
        payment_rows = payment_result.all()

        # 合并所有报告的支付方式数据
        payment_data = {}
        for row in payment_rows:
            # payment_methods_data is a list of dicts like [{"name": "Cash", "value": 100}]
            if row.payment_methods_data:
                for item in row.payment_methods_data:
                    method = item.get("name")
                    value = item.get("value", 0)
                    if method:
                        if method in payment_data:
                            payment_data[method] += float(value)
                        else:
                            payment_data[method] = float(value)

        # 计算总销售额
        total_payment_sales = sum(payment_data.values()) if payment_data else 0

        # 创建支付方式分布数据
        payment_distribution = []
        for method, value in payment_data.items():
            percentage = (value / total_payment_sales * 100) if total_payment_sales > 0 else 0
            payment_distribution.append({
                "name": method,
                "value": float(value),
                "percentage": round(percentage)
            })

        # 创建门店性能数据
        store_performance = []
        if store_id:
            # 如果指定了门店，只返回该门店的数据
            store_query = select(
                Store.id,
                Store.name
            ).where(Store.id == store_id)

            store_result = await db.execute(store_query)
            store_row = store_result.first()

            if store_row:
                store_performance = [
                    {
                        "id": store_row.id,
                        "name": store_row.name,
                        "sales": float(total_sales),
                        "orders": total_orders,
                        "customers": total_customers,
                        "growth": float(sales_growth)
                    }
                ]
        else:
            # 否则返回所有门店的数据
            store_query = select(
                Store.id,
                Store.name,
                func.sum(SalesReport.total_sales).label('sales'),
                func.count(SalesReport.id).label('orders'),
                func.sum(SalesReport.total_customers).label('customers')
            ).join(
                SalesReport, SalesReport.store_id == Store.id
            ).where(
                and_(
                    SalesReport.project_id == project_id,
                    SalesReport.report_date >= start_date,
                    SalesReport.report_date <= end_date
                )
            ).group_by(Store.id, Store.name)

            store_result = await db.execute(store_query)
            store_rows = store_result.all()

            for row in store_rows:
                store_performance.append({
                    "id": row.id,
                    "name": row.name,
                    "sales": float(row.sales) if row.sales is not None else 0.0,
                    "orders": row.orders if row.orders is not None else 0,
                    "customers": row.customers if row.customers is not None else 0,
                    "growth": 5.0  # 假设增长率
                })

        # 创建产品性能数据
        product_performance = []
        for i, product in enumerate(product_ranking[:5]):  # 只取前5个产品
            product_performance.append({
                "id": product['id'],
                "name": product['name'],
                "sales": product['amount'],
                "quantity": product['quantity'],
                "profit": product['amount'] * 0.3,  # 假设利润是销售额的30%
                "growth": 10.0 - i  # 假设增长率
            })

        # --- 支付方式分布 ---
        payment_query = select(SalesReportChannel.payment_methods_details).join(
            SalesReport, SalesReportChannel.sales_report_id == SalesReport.id
        ).where(and_(*base_conditions, SalesReportChannel.payment_methods_details.is_not(None)))
        
        payment_result = await db.execute(payment_query)
        payment_rows = payment_result.scalars().all()
        
        payment_data = defaultdict(float)
        for details_list in payment_rows:
            if isinstance(details_list, list):
                for item in details_list:
                    if isinstance(item, dict) and "name" in item and "value" in item:
                        payment_data[item["name"]] += float(item.get("value", 0))

        payment_distribution = [
            PaymentDistribution(name=name, value=value) for name, value in payment_data.items()
        ]

        # --- 渠道销售占比 ---
        channel_query = select(
            SalesChannel.custom_name,
            func.sum(SalesReportChannel.total_sales).label("total_channel_sales")
        ).select_from(SalesReport).join(
            SalesReportChannel, SalesReport.id == SalesReportChannel.sales_report_id
        ).join(
            SalesChannel, SalesReportChannel.sales_channel_id == SalesChannel.id
        ).where(and_(*base_conditions)).group_by(SalesChannel.custom_name)

        channel_result = await db.execute(channel_query)
        channel_sales_data = channel_result.all()

        channel_sales = [
            ChannelSales(name=row.name, value=row.total_channel_sales) for row in channel_sales_data
        ]

        # --- 渠道销售额趋势 ---
        channel_sales_over_time_query = select(
            SalesReport.report_date,
            SalesChannel.custom_name.label('channel_name'),
            func.sum(SalesReportChannel.total_sales).label('daily_sales')
        ).select_from(SalesReport).join(
            SalesReportChannel, SalesReport.id == SalesReportChannel.sales_report_id
        ).join(
            SalesChannel, SalesReportChannel.sales_channel_id == SalesChannel.id
        ).where(and_(*base_conditions)).group_by(
            SalesReport.report_date, SalesChannel.custom_name
        ).order_by(SalesReport.report_date)
        
        channel_sales_over_time_result = await db.execute(channel_sales_over_time_query)
        channel_sales_over_time_rows = channel_sales_over_time_result.all()

        # 获取充值售卡数据
        # 使用 base_conditions 确保应用门店过滤条件
        recharge_query = select(
            func.sum(SalesReport.recharge_amount).label('recharge_amount'),
            func.sum(SalesReport.card_sales_amount).label('card_sales_amount'),
            func.sum(SalesReport.recharge_count).label('recharge_count'),
            func.sum(SalesReport.card_sales_count).label('card_sales_count')
        ).where(and_(*base_conditions))

        recharge_result = await db.execute(recharge_query)
        recharge_row = recharge_result.first()

        # 创建充值售卡数据
        recharge_data = {
            "recharge_amount": float(recharge_row.recharge_amount or 0),
            "card_sales_amount": float(recharge_row.card_sales_amount or 0),
            "recharge_count": int(recharge_row.recharge_count or 0),
            "card_sales_count": int(recharge_row.card_sales_count or 0)
        }

        # 构建响应数据
        return {
            'last_updated': datetime.now(),
            'total_sales': float(total_sales),
            'total_orders': total_orders,
            'total_customers': total_customers,
            'average_order_value': float(total_sales / total_orders) if total_orders > 0 else 0,
            'customer_conversion_rate': 0.35,  # 假设数据
            'sales_growth': float(sales_growth),
            'order_growth': float(order_growth),
            'customer_growth': float(customer_growth),

            'today_sales': {
                'value': float(today_sales),
                'change': float(sales_change),
                'is_increase': sales_change >= 0
            },
            'today_orders': {
                'value': today_orders,
                'change': float(orders_change),
                'is_increase': orders_change >= 0
            },
            'today_customers': {
                'value': today_customers,
                'change': float(customers_change),
                'is_increase': customers_change >= 0
            },
            'today_average_order': {
                'value': float(today_average_order_value),
                'change': float(average_order_change),
                'is_increase': average_order_change >= 0
            },

            'sales_trend': sales_trend,
            'category_distribution': category_distribution,
            'payment_distribution': payment_distribution,
            'store_performance': store_performance,
            'product_performance': product_performance,
            'channel_sales': channel_sales,
            'recharge_data': recharge_data,
            'product_ranking': product_ranking,
            'task_completion': task_completion,
            'ai_recommendations': ai_recommendations
        }

    @staticmethod
    async def get_dashboard_ai_recommendations(
        db: AsyncSession,
        project_id: uuid.UUID,
        dashboard_type: str
    ) -> List[Dict[str, Any]]:
        """获取仪表盘AI推荐"""
        # 根据仪表盘类型构建不同的提示词
        prompts = {
            "project": "分析项目整体运营情况，提供管理建议",
            "operations": "分析销售和运营数据，提供提升销售和客户体验的建议",
            "finance": "分析财务数据，提供提升利润和控制成本的建议",
            "warehouse": "分析库存数据，提供库存优化和仓储管理的建议",
            "purchase": "分析采购数据，提供优化采购流程和供应商管理的建议",
            "store": "分析门店销售数据，提供提升门店业绩的建议"
        }

        # 获取提示词
        prompt = prompts.get(dashboard_type, "提供业务优化建议")

        # 获取项目信息
        project_query = select(Project).where(Project.id == project_id)
        project_result = await db.execute(project_query)
        project = project_result.scalars().first()

        if not project:
            logger.error(f"项目 ID '{project_id}' 不存在")
            return []

        # 尝试从数据库获取已有的AI推荐
        recommendations_query = select(Notification).where(
            and_(
                Notification.project_id == project_id,
                Notification.type == "ai_recommendation",
                Notification.category == dashboard_type
            )
        ).order_by(desc(Notification.created_at)).limit(5)

        recommendations_result = await db.execute(recommendations_query)
        recommendation_rows = recommendations_result.scalars().all()

        # 如果数据库中有推荐，直接返回
        if recommendation_rows:
            recommendations = []
            for row in recommendation_rows:
                try:
                    # 尝试解析内容
                    content_data = row.content_data or {}
                    recommendations.append({
                        "id": str(row.id),
                        "title": row.title,
                        "content": row.content,
                        "type": content_data.get("type", "业务优化"),
                        "action_items": content_data.get("action_items", []),
                        "priority": content_data.get("priority", "中"),
                        "created_at": row.created_at
                    })
                except Exception as e:
                    logger.error(f"解析AI推荐内容失败: {e}")

            return recommendations

        # 如果没有现有推荐，尝试调用AI服务生成新推荐
        try:
            # 这里应该调用真实的AI服务
            # 由于AI服务可能需要时间处理，这里先返回一些基本建议
            # 在实际应用中，应该实现异步生成推荐并保存到数据库

            # 根据仪表盘类型返回不同的默认建议
            if dashboard_type == "store":
                recommendations = [
                    {
                        "id": str(uuid.uuid4()),
                        "title": "增加周末促销活动",
                        "content": "数据显示周末客流量高但转化率低，建议增加针对性促销活动提高销售额。",
                        "type": "销售增长",
                        "action_items": ["设计周末专属促销方案", "培训销售人员推广技巧"],
                        "priority": "高",
                        "created_at": datetime.now()
                    }
                ]
            elif dashboard_type == "operations":
                recommendations = [
                    {
                        "id": str(uuid.uuid4()),
                        "title": "优化商品陈列",
                        "content": "根据销售数据分析，建议调整热销商品的陈列位置，提高客户购买率。",
                        "type": "业务优化",
                        "action_items": ["分析热销商品数据", "重新规划商品陈列"],
                        "priority": "中",
                        "created_at": datetime.now()
                    }
                ]
            else:
                recommendations = [
                    {
                        "id": str(uuid.uuid4()),
                        "title": "数据分析中",
                        "content": "系统正在分析您的业务数据，稍后将提供个性化建议。",
                        "type": "系统通知",
                        "action_items": ["稍后刷新查看详细建议"],
                        "priority": "低",
                        "created_at": datetime.now()
                    }
                ]

            # 保存推荐到数据库，以便下次直接使用
            for rec in recommendations:
                try:
                    # 查询项目管理员作为通知接收者
                    admin_query = select(ProjectUser.user_id).where(
                        and_(
                            ProjectUser.project_id == project_id,
                            ProjectUser.is_admin == True
                        )
                    ).limit(1)
                    admin_result = await db.execute(admin_query)
                    admin_id = admin_result.scalar()

                    # 如果没有找到项目管理员，使用系统管理员ID
                    if not admin_id:
                        system_admin_query = select(User.id).where(User.is_system_admin == True).limit(1)
                        system_admin_result = await db.execute(system_admin_query)
                        admin_id = system_admin_result.scalar()

                    # 如果仍然没有找到用户，使用默认ID
                    if not admin_id:
                        # 查询任意一个用户ID
                        any_user_query = select(User.id).limit(1)
                        any_user_result = await db.execute(any_user_query)
                        admin_id = any_user_result.scalar()

                    notification = Notification(
                        project_id=project_id,
                        user_id=admin_id,  # 使用找到的用户ID
                        type="ai_recommendation",
                        category=dashboard_type,
                        title=rec["title"],
                        content=rec["content"],
                        content_data={
                            "type": rec["type"],
                            "action_items": rec["action_items"],
                            "priority": rec["priority"]
                        },
                        is_read=False
                    )
                    db.add(notification)
                except Exception as e:
                    logger.error(f"保存AI推荐到数据库失败: {e}")

            try:
                await db.commit()
            except Exception as e:
                logger.error(f"提交AI推荐到数据库失败: {e}")
                await db.rollback()

            return recommendations
        except Exception as e:
            logger.error(f"获取AI推荐失败: {e}")
            return []

    @staticmethod
    async def get_total_sales(db: AsyncSession, project_id: uuid.UUID) -> float:
        """获取项目总销售额"""
        # 获取销售数据
        sales_query = select(
            func.sum(SalesReport.total_sales)
        ).where(
            SalesReport.project_id == project_id
        )
        sales_result = await db.execute(sales_query)
        total_sales = sales_result.scalar() or 0
        return total_sales

    async def get_store_kpis(self, db: AsyncSession, project_id: uuid.UUID, store_id: uuid.UUID, query_date: date) -> Dict[str, Any]:
        """
        获取单个门店在特定日期的核心KPI，数据源为审核过的销售上报
        """
        start_of_day = query_date
        
        base_conditions = [
            SalesReport.project_id == project_id,
            SalesReport.store_id == store_id,
            cast(SalesReport.report_date, Date) == start_of_day,
            SalesReport.status == 'approved'
        ]

        # --- 核心KPI查询 ---
        kpi_query = select(
            func.sum(SalesReport.total_sales).label("total_sales"),
            func.sum(SalesReport.total_orders).label("total_orders"),
            func.sum(SalesReport.total_recharge_amount).label("total_recharge_sales")
        ).where(and_(*base_conditions))
        
        kpi_result = await db.execute(kpi_query)
        kpis = kpi_result.first()

        total_sales = kpis.total_sales or 0
        total_orders = kpis.total_orders or 0
        total_recharge_sales = kpis.total_recharge_sales or 0
        avg_ticket_price = total_sales / total_orders if total_orders > 0 else 0

        # --- 销售目标查询 ---
        target_query = select(StoreSalesTarget.target_amount).where(
            StoreSalesTarget.store_id == store_id,
            StoreSalesTarget.month == query_date.strftime('%Y-%m')
        )
        target_result = await db.execute(target_query)
        monthly_target = target_result.scalar_one_or_none() or 0
        completion_rate = (total_sales / (monthly_target / 30)) * 100 if monthly_target > 0 else 0

        # --- 渠道销售占比 ---
        channel_query = select(
            SalesChannel.custom_name,
            func.sum(SalesReportChannel.total_sales).label("total_channel_sales")
        ).select_from(SalesReport).join(
            SalesReportChannel, SalesReport.id == SalesReportChannel.sales_report_id
        ).join(
            SalesChannel, SalesReportChannel.sales_channel_id == SalesChannel.id
        ).where(and_(*base_conditions)).group_by(SalesChannel.custom_name)

        channel_result = await db.execute(channel_query)
        channel_sales_distribution = [{"channel_name": row.custom_name, "sales": row.total_channel_sales or 0} for row in channel_result.all()]
        
        # --- 支付方式分布 ---
        payment_query = select(
            text("jsonb_array_elements(payment_methods_details)->>'payment_method_name' as name"),
            func.sum(cast(text("jsonb_array_elements(payment_methods_details)->>'amount'"), Float)).label("total_amount")
        ).select_from(SalesReport).join(
             SalesReportChannel, SalesReport.id == SalesReportChannel.sales_report_id
        ).where(and_(*base_conditions)).group_by(text("name"))
        
        payment_result = await db.execute(payment_query)
        payment_method_distribution = [{"payment_method": row.name, "amount": row.total_amount or 0} for row in payment_result.all()]

        return {
            "total_sales": total_sales,
            "total_orders": total_orders,
            "avg_ticket_price": avg_ticket_price,
            "sales_target": monthly_target,
            "completion_rate": completion_rate,
            "total_recharge_sales": total_recharge_sales,
            "channel_sales_distribution": channel_sales_distribution,
            "payment_method_distribution": payment_method_distribution,
        }

    async def get_operation_kpis(self, db: AsyncSession, project_id: uuid.UUID, query_date: date) -> Dict[str, Any]:
        """
        获取运营大盘核心KPI指标
        """
        yesterday_date = query_date - timedelta(days=1)

        # Base query for approved sales reports
        def get_sales_query(target_date: date):
            return select(
                func.sum(SalesReport.total_sales).label("total_sales"),
                func.sum(SalesReport.total_orders).label("total_orders")
            ).where(
                SalesReport.project_id == project_id,
                cast(SalesReport.report_date, Date) == target_date,
                SalesReport.status == 'approved'
            )

        # Sales for today and yesterday
        sales_today_result = await db.execute(get_sales_query(query_date))
        sales_today_data = sales_today_result.first()
        sales_today = sales_today_data.total_sales or 0
        
        sales_yesterday_result = await db.execute(get_sales_query(yesterday_date))
        sales_yesterday_data = sales_yesterday_result.first()
        sales_yesterday = sales_yesterday_data.total_sales or 0
        
        # Loss
        loss_query = select(func.sum(Loss.total_amount)).where(
            Loss.project_id == project_id,
            cast(Loss.created_at, Date) == query_date,
            Loss.status == 'approved'
        )
        total_loss = await db.scalar(loss_query) or 0

        # Sales Target
        target_query = select(func.sum(StoreSalesTarget.target_amount)).join(
            Store, Store.id == StoreSalesTarget.store_id
        ).where(
            Store.project_id == project_id,
            StoreSalesTarget.month == query_date.strftime('%Y-%m')
        )
        total_target = await db.scalar(target_query) or 0
        sales_target_completion = (sales_today / total_target) * 100 if total_target > 0 else 0

        return {
            "totalRevenue": {"value": sales_today, "vsYesterday": sales_today - sales_yesterday, "vsLastWeek": 0},
            "totalLoss": {"value": total_loss, "vsYesterday": 0, "vsLastWeek": 0},
            "avgPriceFluctuation": {"value": 0, "vsYesterday": 0, "vsLastWeek": 0}, # Placeholder
            "totalCustomers": {"value": 0, "vsYesterday": 0, "vsLastWeek": 0}, # Placeholder
            "salesTarget": {"total": total_target, "completed": sales_today, "percentage": round(sales_target_completion, 2)}
        }
